import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { useContext, useMemo } from 'react';
import { camelCaseString } from '../../helperFunction';
import { useGetFormatsQuery } from '../../slices/productFormatsApiSlice';
import { Store } from '../../store/Store';
import SelectV2 from '../global/components/SelectV2';
import Tooltip from '../global/components/ToolTip';
import { formatCurrency } from './utils';

const colors = [
  '#2563eb',
  '#059669',
  '#d97706',
  '#dc2626',
  '#7c3aed',
  '#0891b2',
  '#ea580c',
  '#16a34a',
  '#ca8a04',
  '#9333ea',
];

const getConversionRateContext = (rate) => {
  if (rate >= 70)
    return {
      label: 'Excellent',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    };
  if (rate >= 50)
    return { label: 'Good', color: 'text-green-600', bgColor: 'bg-green-50' };
  if (rate >= 30)
    return {
      label: 'Average',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    };
  return {
    label: 'Needs Attention',
    color: 'text-red-600',
    bgColor: 'bg-red-50',
  };
};

const TopCustomers = ({ data, topCustomerFilter, setTopCustomerFilter }) => {
  const { data: productFormatsData } = useGetFormatsQuery();

  const { defaults } = useContext(Store);

  const isProductFormatVisible =
    defaults?.defaultParam?.projectDefaults?.showProductFormatTable;

  const uniqueChargesFromFormat = useMemo(() => {
    if (!productFormatsData) {
      return [];
    }
    const uniqueChargesFromFormat = [];
    productFormatsData?.productTablesFormats?.forEach((format) => {
      if (format.charges) {
        format.charges.forEach((field) => {
          if (field.chargeType === 'formula') {
            if (!uniqueChargesFromFormat.includes(field.chargeName)) {
              uniqueChargesFromFormat.push(field.chargeName);
            }
          }
        });
      }
    });

    return uniqueChargesFromFormat;
  }, [productFormatsData]);

  const metrics = useMemo(() => {
    if (!data || data.length === 0) {
      return {
        maxTotalValue: 0,
        maxApprovedValue: 0,
        maxConversionRate: 0,
        totalCustomers: 0,
        totalValue: 0,
        totalApprovedValue: 0,
        avgConversionRate: 0,
      };
    }

    const maxTotalValue = Math.max(
      ...data.map((customer) => customer.totalValue)
    );
    const maxApprovedValue = Math.max(
      ...data.map((customer) => customer.approvedValue)
    );
    const maxConversionRate = Math.max(
      ...data.map((customer) => customer.conversionRate)
    );
    const totalCustomers = data.length;
    const totalValue = data.reduce(
      (sum, customer) => sum + customer.totalValue,
      0
    );
    const totalApprovedValue = data.reduce(
      (sum, customer) => sum + customer.approvedValue,
      0
    );
    const avgConversionRate =
      data.reduce((sum, customer) => sum + customer.conversionRate, 0) /
      totalCustomers;

    return {
      maxTotalValue,
      maxApprovedValue,
      maxConversionRate,
      totalCustomers,
      totalValue,
      totalApprovedValue,
      avgConversionRate: Math.round(avgConversionRate * 10) / 10,
    };
  }, [data]);

  const handleSortToggle = () => {
    setTopCustomerFilter((prev) => ({
      ...prev,
      order: prev.order === 'desc' ? 'asc' : 'desc',
    }));
  };

  if (!data || data.length === 0) {
    return (
      <motion.div
        className="h-full flex items-center justify-center text-gray-500"
        style={{ height: '400px' }} // Fixed height for empty state
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
          </div>
          <p className="text-sm font-medium text-gray-900 mb-1">
            No customer data available
          </p>
          <p className="text-xs text-gray-500">
            Customer data will appear here once available
          </p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="h-full flex flex-col"
      style={{ height: '400px' }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="flex-shrink-0 pb-3 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <div className="flex-1">
            {isProductFormatVisible && (
              <SelectV2
                options={[
                  { label: 'Total Quotations', value: 'totalQuotations' },
                  ...(uniqueChargesFromFormat || []).map((name) => ({
                    label: name,
                    value: camelCaseString(name),
                  })),
                ]}
                onChange={(e) => {
                  const val = e.target.value;
                  setTopCustomerFilter((prev) => ({
                    ...prev,
                    by: val,
                  }));
                }}
                value={topCustomerFilter.by}
                size="small"
                placeholder="Sort by..."
              />
            )}
          </div>
          <Tooltip
            text={`Sort ${topCustomerFilter.order === 'desc' ? 'Ascending' : 'Descending'}`}
          >
            <button
              onClick={handleSortToggle}
              className="flex items-center justify-center w-8 h-8 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors duration-200"
            >
              {topCustomerFilter.order === 'desc' ? (
                <SortDescendingOutlined className="text-gray-600 text-sm" />
              ) : (
                <SortAscendingOutlined className="text-gray-600 text-sm" />
              )}
            </button>
          </Tooltip>
        </div>

        <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
          <span>{metrics.totalCustomers} customers</span>
          <span>Total: {formatCurrency(metrics.totalValue)}</span>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto overflow-x-hidden mt-3 pr-1">
        <div className="space-y-2">
          {data.map((customer, index) => {
            const totalValuePercentage =
              (customer.totalValue / metrics.maxTotalValue) * 100;
            const approvedPercentage =
              (customer.approvedValue / customer.totalValue) * 100;
            const color = colors[index % colors.length];

            return (
              <motion.div
                key={customer.customerId}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                className="group relative p-3 rounded-lg border border-gray-100 hover:border-gray-200 hover:shadow-md bg-white transition-all duration-200"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div
                      className="w-2 h-2 rounded-full flex-shrink-0"
                      style={{ backgroundColor: color }}
                    />
                    <Tooltip text={customer.companyName}>
                      <h4 className="text-sm font-semibold text-gray-900 truncate">
                        {customer.companyName}
                      </h4>
                    </Tooltip>
                  </div>
                  <div
                    className={`${isProductFormatVisible && topCustomerFilter.by === 'totalQuotations' ? 'hidden' : 'text-sm font-bold text-gray-900 flex-shrink-0'}`}
                  >
                    {formatCurrency(customer.totalValue)}
                  </div>
                </div>

                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3 text-xs text-gray-600">
                    <span className="font-medium">
                      {customer?.totalQuotations || customer?.totalOrders}{' '}
                      {customer?.totalQuotations ? 'quotations' : 'orders'}
                    </span>
                    <Tooltip
                      text={`Conversion Rate: ${getConversionRateContext(customer.conversionRate).label}`}
                    >
                      <span
                        className={`font-medium px-2 py-1 rounded-full text-xs ${getConversionRateContext(customer.conversionRate).color} ${getConversionRateContext(customer.conversionRate).bgColor}`}
                      >
                        {customer.conversionRate}%
                      </span>
                    </Tooltip>
                  </div>
                </div>

                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-1">
                    <Tooltip
                      text={`${customer?.approved || 0} approved ${customer?.totalQuotations ? 'quotations' : 'orders'}`}
                    >
                      <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-green-50 hover:bg-green-100 transition-colors">
                        <CheckCircleOutlined className="text-green-600 text-xs" />
                        <span className="text-xs font-medium text-green-700">
                          {customer?.approved || 0}
                        </span>
                      </div>
                    </Tooltip>
                    <Tooltip
                      text={`${customer?.pending || 0} pending ${customer?.totalQuotations ? 'quotations' : 'orders'}`}
                    >
                      <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-orange-50 hover:bg-orange-100 transition-colors">
                        <ClockCircleOutlined className="text-orange-600 text-xs" />
                        <span className="text-xs font-medium text-orange-700">
                          {customer?.pending || 0}
                        </span>
                      </div>
                    </Tooltip>
                    <Tooltip
                      text={`${customer?.rejected || 0} rejected ${customer?.totalQuotations ? 'quotations' : 'orders'}`}
                    >
                      <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-red-50 hover:bg-red-100 transition-colors">
                        <CloseCircleOutlined className="text-red-600 text-xs" />
                        <span className="text-xs font-medium text-red-700">
                          {customer?.rejected || 0}
                        </span>
                      </div>
                    </Tooltip>
                  </div>
                  <div className="text-xs text-gray-500">#{index + 1}</div>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Progress</span>
                    <span>
                      {formatCurrency(customer.approvedValue)} /{' '}
                      {formatCurrency(customer.totalValue)}
                    </span>
                  </div>
                  <Tooltip
                    text={`Total: ${formatCurrency(customer.totalValue)} | Approved: ${formatCurrency(customer.approvedValue)} (${approvedPercentage.toFixed(1)}%)`}
                  >
                    <div className="relative h-2 bg-gray-100 rounded-full overflow-hidden">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${totalValuePercentage}%` }}
                        transition={{
                          delay: index * 0.05 + 0.2,
                          duration: 0.8,
                          ease: 'easeOut',
                        }}
                        className="absolute top-0 left-0 h-full rounded-full opacity-30"
                        style={{ backgroundColor: color }}
                      />
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{
                          width: `${(customer.approvedValue / metrics.maxTotalValue) * 100}%`,
                        }}
                        transition={{
                          delay: index * 0.05 + 0.3,
                          duration: 0.8,
                          ease: 'easeOut',
                        }}
                        className="absolute top-0 left-0 h-full rounded-full"
                        style={{ backgroundColor: color }}
                      />
                    </div>
                  </Tooltip>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </motion.div>
  );
};

export default TopCustomers;
