import {
  FileTextOutlined,
  ReloadOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import { Button, Card, Tooltip } from 'antd';
import { motion } from 'framer-motion';
import { useEffect, useLayoutEffect, useState } from 'react';
import { toast } from 'react-toastify';
import Header from '../components/global/components/Header';
import CustomerFilter from '../components/SalesDashboard/CustomerFilter';
import DateRangeFilter from '../components/SalesDashboard/DateRangeFilter';
import QuotationsTab from '../components/SalesDashboard/QuotationsTab';
import SalesOrdersTab from '../components/SalesDashboard/SalesOrdersTab';
import useQuotationDashboard from '../components/SalesDashboard/useQuotationDashboard';
import useSalesOrderDashboard from '../components/SalesDashboard/useSalesOrderDashboard';

const SalesDashboard = () => {
  const [dateRange, setDateRange] = useState([null, null]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [activeTab, setActiveTab] = useState('quotations');
  const [preferencesLoaded, setPreferencesLoaded] = useState(false);
  const [topCustomerFilter, setTopCustomerFilter] = useState({
    by: activeTab === 'quotations' ? 'totalQuotations' : 'totalSalesOrders',
    limit: 5,
    order: 'desc',
  });

  const quotationDashboard = useQuotationDashboard({
    dateRange,
    selectedCustomer,
    topCustomerFilter,
    isActive: activeTab === 'quotations',
    onDateRangeChange: setDateRange,
    onSelectedCustomerChange: setSelectedCustomer,
    onTopCustomerFilterChange: setTopCustomerFilter,
  });

  const salesOrderDashboard = useSalesOrderDashboard({
    dateRange,
    selectedCustomer,
    topCustomerFilter,
    isActive: activeTab === 'sales-orders',
    onDateRangeChange: setDateRange,
    onSelectedCustomerChange: setSelectedCustomer,
    onTopCustomerFilterChange: setTopCustomerFilter,
  });

  useLayoutEffect(() => {
    setPreferencesLoaded(true);
  }, []);

  useEffect(() => {
    if (preferencesLoaded) {
      if (activeTab === 'quotations') {
        quotationDashboard.fetchAllData();
      } else if (activeTab === 'sales-orders') {
        salesOrderDashboard.fetchAllData();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    activeTab,
    dateRange,
    selectedCustomer,
    preferencesLoaded,
    quotationDashboard.fetchAllData,
    salesOrderDashboard.fetchAllData,
  ]);
  useEffect(() => {
    if (
      preferencesLoaded &&
      (dateRange[0] || dateRange[1] || selectedCustomer !== undefined)
    ) {
      const saveFilterPreferences = async () => {
        try {
          const currentDashboard =
            activeTab === 'quotations'
              ? quotationDashboard
              : salesOrderDashboard;
          if (currentDashboard.savePreferences) {
            await currentDashboard.savePreferences({
              dateRange: {
                startDate: dateRange[0] ? dateRange[0].toDate() : null,
                endDate: dateRange[1] ? dateRange[1].toDate() : null,
              },
              selectedCustomer,
            });
          }
        } catch (error) {
          toast.error('Failed to save filter preferences. Please try again.');
        }
      };
      const timeoutId = setTimeout(saveFilterPreferences, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [
    dateRange,
    selectedCustomer,
    activeTab,
    preferencesLoaded,
    quotationDashboard,
    salesOrderDashboard,
  ]);

  const handleReset = async () => {
    const currentDashboard =
      activeTab === 'quotations' ? quotationDashboard : salesOrderDashboard;
    try {
      if (currentDashboard.savePreferences) {
        await currentDashboard.savePreferences({
          dateRange: {
            startDate: null,
            endDate: null,
          },
          selectedCustomer: null,
        });
      }
      setDateRange([null, null]);
      setSelectedCustomer(null);
    } catch (error) {
      toast.error('Failed to save filter preferences. Please try again.');
    }
  };

  const tabItems = [
    {
      key: 'quotations',
      label: (
        <>
          <FileTextOutlined />
          <span>Quotations</span>
        </>
      ),
    },
    {
      key: 'sales-orders',
      label: (
        <>
          <ShoppingCartOutlined />
          <span>Sales Orders</span>
        </>
      ),
    },
  ];

  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  return (
    <div className="flex flex-col h-[90vh] overflow-hidden">
      <div className="flex-shrink-0">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-2"
        >
          <Header
            title="Sales Dashboard"
            description="Track and analyze your sales performance metrics in real-time"
            infoTitle="Welcome to the Sales Dashboard"
            infoDesc="Your central hub for monitoring quotations and sales orders. Get comprehensive insights into your sales pipeline, track customer interactions, and make data-driven decisions."
            paras={[
              'View and manage quotations with detailed customer information and status tracking',
              'Monitor sales orders from creation to fulfillment',
              'Analyze sales trends with interactive charts and filters',
              'Export data for detailed reporting and analysis',
            ]}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Card
            className="border-0 rounded-b-none rounded-t-lg"
            styles={{ body: { padding: 0 } }}
          >
            <div className="px-4 sm:px-6 pt-4 border-b border-gray-200 bg-white">
              <div className="hidden lg:flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {tabItems.map((item) => (
                    <motion.button
                      key={item.key}
                      onClick={() => handleTabChange(item.key)}
                      className={`flex items-center gap-2 px-4 py-2 font-medium transition-all duration-200 relative ${
                        activeTab === item.key
                          ? 'text-blue-600'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                      }`}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {item.label}
                      {activeTab === item.key && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 rounded-full"
                          transition={{
                            type: 'spring',
                            stiffness: 500,
                            damping: 30,
                          }}
                        />
                      )}
                    </motion.button>
                  ))}
                </div>
                <div className="flex items-center gap-4">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                  >
                    <CustomerFilter
                      selectedCustomer={selectedCustomer}
                      setSelectedCustomer={setSelectedCustomer}
                    />
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                  >
                    <DateRangeFilter
                      dateRange={dateRange}
                      setDateRange={setDateRange}
                    />
                  </motion.div>
                  <Tooltip title="Reset filter">
                    <Button
                      type="text"
                      icon={<ReloadOutlined />}
                      onClick={handleReset}
                      className="text-gray-500 hover:text-blue-600 hover:bg-blue-50 border-0 rounded-lg transition-all duration-200"
                      size="middle"
                    />
                  </Tooltip>
                </div>
              </div>

              <div className="lg:hidden space-y-3 pb-2">
                <div className="flex items-center justify-center space-x-1 sm:space-x-2">
                  {tabItems.map((item) => (
                    <motion.button
                      key={item.key}
                      onClick={() => handleTabChange(item.key)}
                      className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 text-sm sm:text-base font-medium transition-all duration-200 relative ${
                        activeTab === item.key
                          ? 'text-blue-600'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                      }`}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {item.label}
                      {activeTab === item.key && (
                        <motion.div
                          layoutId="activeTabMobile"
                          className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 rounded-full"
                          transition={{
                            type: 'spring',
                            stiffness: 500,
                            damping: 30,
                          }}
                        />
                      )}
                    </motion.button>
                  ))}
                </div>

                <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="flex-1 sm:flex-none"
                  >
                    <CustomerFilter
                      selectedCustomer={selectedCustomer}
                      setSelectedCustomer={setSelectedCustomer}
                    />
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    className="flex-1 sm:flex-none"
                  >
                    <DateRangeFilter
                      dateRange={dateRange}
                      setDateRange={setDateRange}
                    />
                  </motion.div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>

      <div className="flex-1 overflow-y-auto bg-white">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card
            className="bg-white border-0 border-t-0 rounded-t-none rounded-b-lg "
            styles={{ body: { padding: 0 } }}
          >
            <div className="tab-content">
              {activeTab === 'quotations' && (
                <div className="px-4 sm:px-6 py-4 sm:py-6">
                  <QuotationsTab
                    dateRange={dateRange}
                    selectedCustomer={selectedCustomer}
                    dashboardData={quotationDashboard}
                    topCustomerFilter={topCustomerFilter}
                    setTopCustomerFilter={setTopCustomerFilter}
                  />
                </div>
              )}
              {activeTab === 'sales-orders' && (
                <div className="px-4 sm:px-6 py-4 sm:py-6">
                  <SalesOrdersTab
                    dateRange={dateRange}
                    selectedCustomer={selectedCustomer}
                    dashboardData={salesOrderDashboard}
                    topCustomerFilter={topCustomerFilter}
                    setTopCustomerFilter={setTopCustomerFilter}
                  />
                </div>
              )}
            </div>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default SalesDashboard;
