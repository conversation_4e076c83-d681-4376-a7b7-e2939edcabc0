const AccountManagement = ({ defaults, setDefaults }) => {
  return (
    <div className="w-[100%] border-t-2 my-3 border-gray-400/70  ">
      <div className="grid grid-cols-1 md:grid-cols-2 w-full py-2 justify-center items-center">
        <h3 className="text-gray-subHeading">Account Management :</h3>
      </div>
      <div className=" grid grid-cols-3 w-full py-2 text-sm justify-center items-center gap-y-2 ">
        <div className="flex items-center text-sm gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.purchaseVoucherAutoCreate}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  purchaseVoucherAutoCreate: e.target.checked,
                },
              }));
            }}
          />
          <p>Purchase Voucher Auto Create</p>
        </div>
        <div className="flex items-center text-sm gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.salesVoucherAutoCreate}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  salesVoucherAutoCreate: e.target.checked,
                },
              }));
            }}
          />
          <p>Sales Voucher Auto Create</p>
        </div>
        <div className="flex items-center text-sm gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.journalVoucherAutoCreate}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  journalVoucherAutoCreate: e.target.checked,
                },
              }));
            }}
          />
          <p>Journal Voucher Auto Create</p>
        </div>
      </div>
    </div>
  );
};

export default AccountManagement;
