import { motion } from 'framer-motion';
import {
  Area,
  AreaChart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

const UserStatsAreaChart = ({ data = [], height }) => {
  const chartData = data?.map((user, index) => ({
    name:
      user.created_by.length > 15
        ? user.created_by.substring(0, 15) + '...'
        : user.created_by,
    fullName: user.created_by,
    totalCreations: user.totalCreation,
    index: index + 1,
  }));

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;

      return (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg px-3 py-2 shadow-lg min-w-[160px]"
        >
          <div className="mb-2">
            <p className="text-gray-800 font-semibold text-sm">
              {data.fullName}
            </p>
          </div>

          <div className="space-y-2">
            {payload.map((entry, index) => {
              const displayName = 'Total Creations';
              const value =
                typeof entry.value === 'number'
                  ? entry.value.toLocaleString()
                  : entry.value;

              return (
                <div key={index} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <div
                        className="w-2 h-2 rounded-full shadow-sm"
                        style={{ backgroundColor: entry.color }}
                      />
                      <span className="text-gray-600 text-xs font-medium">
                        {displayName}
                      </span>
                    </div>
                    <span className="text-gray-900 font-bold text-sm">
                      {value}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </motion.div>
      );
    }
    return null;
  };

  const CustomLegend = ({ payload }) => {
    if (!payload || payload.length === 0) return null;

    const totalItems =
      data?.reduce((sum, user) => sum + user.totalCreation, 0) || 0;
    const topPerformer = data?.reduce((max, user) =>
      user.totalCreation > max.totalCreation ? user : max
    );
    const avgCreations =
      data?.length > 0 ? Math.round(totalItems / data.length) : 0;

    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.4 }}
        className="flex items-center justify-between pt-4 border-t border-gray-100 mt-4"
      >
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-2">
            <div className="w-4 h-3 bg-gradient-to-r from-blue-500 to-blue-400 rounded-sm shadow-sm"></div>
            <span className="text-sm font-medium text-gray-700">
              Total Creations
            </span>
          </div>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span>
              Total:{' '}
              <span className="font-semibold text-gray-900">
                {totalItems.toLocaleString()}
              </span>
            </span>
            <span>
              Avg:{' '}
              <span className="font-semibold text-gray-900">
                {avgCreations}
              </span>
            </span>
          </div>
        </div>

        {topPerformer && (
          <div className="text-sm text-gray-600">
            Top performer:{' '}
            <span className="font-semibold text-gray-900">
              {topPerformer.created_by}
            </span>
            <span className="ml-1 text-gray-500">
              ({topPerformer.totalCreation.toLocaleString()} items)
            </span>
          </div>
        )}
      </motion.div>
    );
  };

  return (
    <motion.div
      className="w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      <motion.div
        style={{ height: `${height}px` }}
        className="w-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <defs>
              <linearGradient id="colorCreations" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.05} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
            <XAxis
              dataKey="name"
              tick={{ fontSize: 12, fill: '#64748b' }}
              tickLine={{ stroke: '#cbd5e1' }}
              axisLine={{ stroke: '#cbd5e1' }}
              angle={-45}
              textAnchor="end"
              height={60}
            />
            <YAxis
              tick={{ fontSize: 12, fill: '#64748b' }}
              tickLine={{ stroke: '#cbd5e1' }}
              axisLine={{ stroke: '#cbd5e1' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="totalCreations"
              stroke="#3b82f6"
              strokeWidth={2}
              fill="url(#colorCreations)"
              dot={{
                fill: '#3b82f6',
                strokeWidth: 2,
                r: 4,
                stroke: '#ffffff',
              }}
              activeDot={{
                r: 6,
                stroke: '#3b82f6',
                strokeWidth: 2,
                fill: '#ffffff',
                shadow: true,
              }}
            />
            <Legend content={<CustomLegend />} />
          </AreaChart>
        </ResponsiveContainer>
      </motion.div>
    </motion.div>
  );
};

export default UserStatsAreaChart;
