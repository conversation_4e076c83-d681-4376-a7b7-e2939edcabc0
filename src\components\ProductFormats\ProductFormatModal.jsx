import { FormOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input, Modal, Tabs } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { v4 as uuidv4 } from 'uuid';
import MultiSelect from '../global/components/MultiSelect';
import ChargeItem from './ChargeItem';
import ColumnTotalField from './ColumnTotalField';
import Condition from './Condition';
import FieldItem from './FieldItem';
import { defaultFields } from './defaultFields';

const { TabPane } = Tabs;

const ProductFormatModal = ({
  visible,
  onClose,
  onSave,
  initialData,
  copyFormat,
}) => {
  const [form] = Form.useForm();
  const [fields, setFields] = useState([]);
  const [charges, setCharges] = useState([]);
  const [totalFields, setTotalFields] = useState([]);
  const [conditions, setConditions] = useState([]);
  const [activeTab, setActiveTab] = useState('1');

  useEffect(() => {
    if (initialData || copyFormat) {
      const initialFields = (initialData || copyFormat).fields.map((field) => ({
        ...field,
        fieldId: field.fieldId || uuidv4(),
        formulaData: field.formulaData || {
          parts: [],
        },
      }));
      const initialCharges = (initialData || copyFormat).charges.map(
        (charge) => ({
          ...charge,
          chargeId: charge.chargeId || uuidv4(),
          chargeFormulaData: charge.chargeFormulaData || {
            parts: [],
          },
        })
      );
      const initialTotalFields = (initialData || copyFormat).totalFields || [];
      const initialConditions = (initialData || copyFormat).conditions || [];
      setFields(initialFields);
      setCharges(initialCharges);
      setTotalFields(initialTotalFields);
      setConditions(initialConditions);
      form.setFieldsValue({
        formatName: (initialData || copyFormat).formatName,
        createdBy: (initialData || copyFormat).createdBy,
        fields: initialFields,
        charges: initialCharges,
        totalFields: initialTotalFields,
        conditions: initialConditions,
      });
    } else {
      setFields([]);
      setCharges([]);
      setTotalFields([]);
      setConditions([]);
      form.resetFields();
    }
  }, [initialData, copyFormat, form, visible]);

  const addField = useCallback(() => {
    const newField = {
      fieldId: uuidv4(),
      fieldName: '',
      fieldType: 'text',
      formulaData: { parts: [] },
      isDefault: false,
      formatting: {
        isItPrice: false,
        isItPercentage: false,
        roundValue: false,
        isEditable: false,
      },
    };
    setFields((prevFields) => {
      const updatedFields = [...prevFields, newField];
      form.setFieldsValue({ fields: updatedFields });
      return updatedFields;
    });
  }, [form]);

  const removeField = useCallback(
    (index) => {
      setFields((prevFields) => {
        const updatedFields = prevFields.filter((_, i) => i !== index);
        form.setFieldsValue({ fields: updatedFields });
        return updatedFields;
      });
    },
    [form]
  );

  const moveField = useCallback(
    (dragIndex, hoverIndex) => {
      setFields((prevFields) => {
        const updatedFields = [...prevFields];
        const [movedField] = updatedFields.splice(dragIndex, 1);
        updatedFields.splice(hoverIndex, 0, movedField);
        form.setFieldsValue({ fields: updatedFields });
        return updatedFields;
      });
    },
    [form]
  );

  const addCharge = useCallback(() => {
    const newCharge = {
      chargeId: uuidv4(),
      chargeName: '',
      chargeType: 'text',
      chargeFormulaData: { parts: [] },
      isDefault: false,
      formatting: {
        isItPrice: false,
        isItPercentage: false,
        convertInWords: false,
        roundValue: false,
      },
    };
    setCharges((prevCharges) => {
      const updatedCharges = [...prevCharges, newCharge];
      form.setFieldsValue({ charges: updatedCharges });
      return updatedCharges;
    });
  }, [form]);
  const addTotalField = useCallback(() => {
    const newTotalField = {
      fieldId: uuidv4(),
      fieldName: '',
      columnToTotal: '',
    };
    setTotalFields((prevTotalFields) => {
      const updatedTotalFields = [...prevTotalFields, newTotalField];
      form.setFieldsValue({ totalFields: updatedTotalFields });
      return updatedTotalFields;
    });
  }, [form]);

  const removeCharge = useCallback(
    (index) => {
      setCharges((prevCharges) => {
        const updatedCharges = prevCharges.filter((_, i) => i !== index);
        form.setFieldsValue({ charges: updatedCharges });
        return updatedCharges;
      });
    },
    [form]
  );

  const moveCharge = useCallback(
    (dragIndex, hoverIndex) => {
      setCharges((prevCharges) => {
        const updatedCharges = [...prevCharges];
        const [movedCharge] = updatedCharges.splice(dragIndex, 1);
        updatedCharges.splice(hoverIndex, 0, movedCharge);
        form.setFieldsValue({ charges: updatedCharges });
        return updatedCharges;
      });
    },
    [form]
  );
  const removeTotalFields = useCallback(
    (index) => {
      setTotalFields((prevTotalFields) => {
        const updatedTotalFields = prevTotalFields.filter(
          (_, i) => i !== index
        );
        form.setFieldsValue({ totalFields: updatedTotalFields });
        return updatedTotalFields;
      });
    },
    [form]
  );
  const moveTotalField = useCallback(
    (dragIndex, hoverIndex) => {
      setTotalFields((prevTotalFields) => {
        const updatedTotalFields = [...prevTotalFields];
        const [movedTotalField] = updatedTotalFields.splice(dragIndex, 1);
        updatedTotalFields.splice(hoverIndex, 0, movedTotalField);
        form.setFieldsValue({ totalFields: updatedTotalFields });
        return updatedTotalFields;
      });
    },
    [form]
  );

  const updateFieldFormatting = useCallback(
    (fieldIndex, formatType, value) => {
      setFields((prevFields) => {
        const updatedFields = [...prevFields];
        const currentField = updatedFields[fieldIndex];

        // Create new formatting object
        const newFormatting = {
          ...currentField.formatting,
          [formatType]: value,
        };

        // If setting isItPrice to true, ensure isItPercentage is false
        if (formatType === 'isItPrice' && value === true) {
          newFormatting.isItPercentage = false;
        }

        // If setting isItPercentage to true, ensure isItPrice is false
        if (formatType === 'isItPercentage' && value === true) {
          newFormatting.isItPrice = false;
        }

        // Update the field with new formatting
        updatedFields[fieldIndex] = {
          ...currentField,
          formatting: newFormatting,
        };

        form.setFieldsValue({ fields: updatedFields });
        return updatedFields;
      });
    },
    [form]
  );

  const updateChargeFormatting = useCallback(
    (chargeIndex, formatType, value) => {
      setCharges((prevCharges) => {
        const updatedCharges = [...prevCharges];
        const currentCharge = updatedCharges[chargeIndex];

        // Create new formatting object
        const newFormatting = {
          ...currentCharge.formatting,
          [formatType]: value,
        };

        // If setting isItPrice to true, ensure isItPercentage is false
        if (formatType === 'isItPrice' && value === true) {
          newFormatting.isItPercentage = false;
        }

        // If setting isItPercentage to true, ensure isItPrice is false
        if (formatType === 'isItPercentage' && value === true) {
          newFormatting.isItPrice = false;
        }

        // Update the charge with new formatting
        updatedCharges[chargeIndex] = {
          ...currentCharge,
          formatting: newFormatting,
        };

        form.setFieldsValue({ charges: updatedCharges });
        return updatedCharges;
      });
    },
    [form]
  );

  const onFinish = (values) => {
    const formatData = {
      ...values,
      fields: fields.map((field) => {
        const baseField = {
          ...field,
          fieldId: field.fieldId,
          fieldName: field.fieldName,
          fieldType: field.fieldType,
          isDefault: field.isDefault,
          formatting: {
            isItPrice: field.formatting?.isItPrice || false,
            isItPercentage: field.formatting?.isItPercentage || false,
            roundValue: field.formatting?.roundValue || false,
          },
          _id: field._id,
        };

        if (field.fieldType === 'formula') {
          baseField.formula = field.formula;
          baseField.formulaData = field.formulaData || { parts: [] };
        }

        if (field.fieldType === 'dropdown') {
          baseField.dropdownOptions = field.dropdownOptions || [];
        }

        return baseField;
      }),
      charges: charges.map((charge) => {
        const baseCharge = {
          ...charge,
          chargeId: charge.chargeId,
          chargeName: charge.chargeName,
          chargeType: charge.chargeType,
          isDefault: charge.isDefault,
          formatting: {
            isItPrice: charge.formatting?.isItPrice || false,
            isItPercentage: charge.formatting?.isItPercentage || false,
            convertInWords: charge.formatting?.convertInWords || false,
            roundValue: charge.formatting?.roundValue || false,
          },
        };

        if (charge.chargeType === 'formula') {
          baseCharge.chargeFormula = charge.chargeFormula;
          baseCharge.chargeFormulaData = charge.chargeFormulaData || {
            parts: [],
          };
        }

        if (charge.chargeType === 'dropdown') {
          baseCharge.chargeDropdownOptions = charge.chargeDropdownOptions || [];
        }

        return baseCharge;
      }),
      totalFields: totalFields,
      conditions: conditions,
    };
    // console.log("formatData", formatData);
    // return

    onSave(formatData);
  };

  return (
    <Modal
      title={
        <div className="flex items-center space-x-2">
          <FormOutlined className="text-blue-500 text-xl" />
          <span className="text-xl font-semibold text-gray-800">
            {initialData ? 'Edit Format' : 'Create Format'}
          </span>
        </div>
      }
      open={visible}
      onCancel={() => {
        onClose();
        setActiveTab('1');
        setFields([]);
        setCharges([]);
        setTotalFields([]);
        setConditions([]);
        form.resetFields();
      }}
      footer={null}
      width={900}
      className="rounded-lg shadow-xl"
      styles={{
        body: {
          maxHeight: 'calc(100vh - 200px)',
          overflowY: 'auto',
        },
      }}
    >
      <DndProvider backend={HTML5Backend}>
        <Form form={form} onFinish={onFinish} layout="vertical">
          <Form.Item
            name="formatName"
            label={
              <span className="text-gray-700 font-medium">Format Name</span>
            }
            rules={[{ required: true, message: 'Format Name is required' }]}
          >
            <Input
              placeholder="Enter format name"
              className="rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 transition-all"
            />
          </Form.Item>
          {/* default fields selection */}
          <MultiSelect
            disabled={activeTab === '2' || activeTab === '3'}
            placeholder="Select default fields"
            options={defaultFields.map((field) => ({
              value: field.fieldName,
              label: field.fieldName,
            }))}
            className="rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 transition-all"
            value={fields
              .filter((field) => field.isDefault)
              .map((field) => ({
                value: field.fieldName,
                label: field.fieldName,
              }))}
            onChange={(e) => {
              const selectedFieldNames = e.target.value.map(
                (item) => item.value
              );
              const updatedFields = fields.filter((field) => !field.isDefault);

              const newSelectedFields = selectedFieldNames.map((fieldName) => {
                const field = defaultFields.find(
                  (defaultField) => defaultField.fieldName === fieldName
                );
                return field;
              });

              const finalFields = [...updatedFields, ...newSelectedFields];
              setFields(finalFields);
              form.setFieldsValue({ fields: finalFields });
            }}
          />
          <Tabs
            activeKey={activeTab}
            onChange={(key) => setActiveTab(key)}
            className="modern-tabs"
            tabBarStyle={{
              borderBottom: '2px solid #e5e7eb',
              marginBottom: '16px',
            }}
          >
            <TabPane
              tab={<span className="text-gray-700 font-medium">Fields</span>}
              key="1"
            >
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.fieldId}>
                    <FieldItem
                      key={field.fieldId}
                      index={index}
                      field={field}
                      moveField={moveField}
                      form={form}
                      onRemove={removeField}
                      fields={fields}
                      setFields={setFields}
                      updateFieldFormatting={updateFieldFormatting}
                    />
                  </div>
                ))}
                <Button
                  type="dashed"
                  onClick={addField}
                  block
                  icon={<PlusOutlined />}
                  className="mt-2 border-blue-400 text-blue-500 hover:bg-blue-50 transition-all rounded-md"
                >
                  Add Field
                </Button>
              </div>
            </TabPane>
            <TabPane
              tab={<span className="text-gray-700 font-medium">Charges</span>}
              key="2"
            >
              <div className="space-y-4">
                {charges.map((charge, index) => (
                  <div key={charge.chargeId}>
                    <ChargeItem
                      index={index}
                      charge={charge}
                      moveCharge={moveCharge}
                      form={form}
                      onRemove={removeCharge}
                      charges={charges}
                      setCharges={setCharges}
                      fields={fields}
                      totalFields={totalFields}
                      updateChargeFormatting={updateChargeFormatting}
                    />
                  </div>
                ))}
                <Button
                  type="dashed"
                  onClick={addCharge}
                  block
                  icon={<PlusOutlined />}
                  className="mt-2 border-blue-400 text-blue-500 hover:bg-blue-50 transition-all rounded-md"
                >
                  Add Charge
                </Button>
              </div>
            </TabPane>
            <TabPane
              tab={
                <span className="text-gray-700 font-medium">
                  Total Column Fields
                </span>
              }
              key="3"
            >
              <div className="space-y-4">
                {totalFields.map((field, index) => (
                  <ColumnTotalField
                    key={field.fieldId}
                    index={index}
                    field={field}
                    form={form}
                    onRemove={removeTotalFields}
                    moveTotalField={moveTotalField}
                    fields={fields}
                    totalFields={totalFields}
                    setTotalFields={setTotalFields}
                  />
                ))}
                <Button
                  type="dashed"
                  onClick={addTotalField}
                  block
                  icon={<PlusOutlined />}
                  className="mt-2 border-blue-400 text-blue-500 hover:bg-blue-50 transition-all rounded-md"
                >
                  Add Field
                </Button>
              </div>
            </TabPane>
            <TabPane
              tab={<span className="text-gray-700 font-medium">Condition</span>}
              key="4"
            >
              <Condition
                fields={fields}
                charges={charges}
                conditions={conditions}
                setConditions={setConditions}
              />
            </TabPane>
          </Tabs>

          <div className="flex justify-end space-x-3 mt-6">
            <Button
              onClick={onClose}
              className="rounded-md border-gray-300 text-gray-700 hover:bg-gray-100 transition-all"
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              {initialData
                ? 'Update Format'
                : copyFormat
                  ? 'Copy Format'
                  : 'Create Format'}
            </Button>
          </div>
        </Form>
      </DndProvider>
    </Modal>
  );
};

export default ProductFormatModal;
