const pagesToDisable = ['quotation', 'indent', 'salesOrder', 'purchaseOrder'];
const DisableApproval = ({ defaults, setDefaults }) => {
  return (
    <div className="w-[100%] border-b-2 border-t-2 my-3 border-gray-400/70  ">
      <div className="grid grid-cols-1 md:grid-cols-2 w-full py-2 justify-center items-center">
        <h3 className="text-gray-subHeading">Disable Approvals :</h3>
        <div className="flex items-center text-sm gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={pagesToDisable?.every((v) =>
              defaults?.projectDefaults?.disabledApprovalFor?.includes(v)
            )}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  disabledApprovalFor: e.target.checked
                    ? ['quotation', 'indent', 'salesOrder', 'purchaseOrder']
                    : [],
                },
              }));
            }}
          />
          <p>Disable All Approvals</p>
        </div>
      </div>
      <div className=" grid grid-cols-2 w-full py-2 text-sm justify-center items-center gap-y-2 ">
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.disabledApprovalFor?.includes(
              'quotation'
            )}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  disabledApprovalFor: e.target.checked
                    ? [...prev.projectDefaults.disabledApprovalFor, 'quotation']
                    : prev.projectDefaults.disabledApprovalFor.filter(
                        (v) => v !== 'quotation'
                      ) || [],
                },
              }));
            }}
          />
          <p>Disable Quotation Approval</p>
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.disabledApprovalFor?.includes(
              'indent'
            )}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  disabledApprovalFor: e.target.checked
                    ? [...prev.projectDefaults.disabledApprovalFor, 'indent']
                    : prev.projectDefaults.disabledApprovalFor.filter(
                        (v) => v !== 'indent'
                      ) || [],
                },
              }));
            }}
          />
          <p>Disable Indent Approval</p>
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.disabledApprovalFor?.includes(
              'salesOrder'
            )}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  disabledApprovalFor: e.target.checked
                    ? [
                        ...prev.projectDefaults.disabledApprovalFor,
                        'salesOrder',
                      ]
                    : prev.projectDefaults.disabledApprovalFor.filter(
                        (v) => v !== 'salesOrder'
                      ) || [],
                },
              }));
            }}
          />
          <p>Disable Sales-Order Approval</p>
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={defaults?.projectDefaults?.disabledApprovalFor?.includes(
              'purchaseOrder'
            )}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  disabledApprovalFor: e.target.checked
                    ? [
                        ...prev.projectDefaults.disabledApprovalFor,
                        'purchaseOrder',
                      ]
                    : prev.projectDefaults.disabledApprovalFor.filter(
                        (v) => v !== 'purchaseOrder'
                      ) || [],
                },
              }));
            }}
          />
          <p>Disable Purchase-Order Approval</p>
        </div>
      </div>
    </div>
  );
};

export default DisableApproval;
