import { useCallback, useContext, useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Store } from '../../../store/Store';
import {
  generatePrefixId,
  renderFieldsBasedOnType,
} from '../../../helperFunction';
import { useGetAssemblyFormsQuery } from '../../../slices/assemblyFormApiSlice';
import {
  useCreatePoV2Mutation,
  useEditWorkOrderMutation,
} from '../../../slices/createPoApiSlice';
import { useQueryTemplateByIdQuery } from '../../../slices/dsahboardTemplateApiSlice';
import {
  useGetMediaByIdArrayMutation,
  useLazyGetMediaByIdQuery,
} from '../../../slices/mediaSlice';
import {
  useCreateOrderMutation,
  useLazyGetOrderDetailsByIDQuery,
} from '../../../slices/orderApiSlice';
import { useGetAllPartsForOptionsQuery } from '../../../slices/partApiSlice';
import { useGetAllProductsForOptionsQuery } from '../../../slices/productApiSlice';
import {
  useGetSalesOrderOptionsQuery,
  useLazyGetSalesOrderByIdQuery,
} from '../../../slices/salesOrderSlices';

import { Button as AntdButton, Radio } from 'antd';
import usePrefixIds from '../../../hooks/usePrefixIds';
import MediaPreviewModal from '../../DispatchV2/MediaPreviewModal';
import Button from '../../global/components/Button';
import Input from '../../global/components/Input';
import Select from '../../global/components/Select';
import UploadButton from '../../UploadButton';
import CreateWorkOrderItemsTable from './CreateWorkOrderItemsTable';
import WorkOrderActions from './WorkOrderActions';

const Label = ({ children }) => {
  return (
    <label className="block mb-1 text-sm text-gray-500 font-medium">
      {children}
    </label>
  );
};

const CreateWorkOrderV2 = ({ closeForm, allData, editRecord, isCopy }) => {
  const [Searchparams] = useSearchParams();
  const navigate = useNavigate();
  const {
    state,
    defaults: { defaultParam },
    dispatch,
  } = useContext(Store);
  const [createDepOrder] = useCreateOrderMutation();
  const [attachments, setAttachments] = useState([]);
  const [itemsData, setItemsData] = useState([]);
  const [inputData, setInputData] = useState({
    template: '',
    name: '',
    additionalFields: {},
    orderQuantity: 1,
  });

  const [openMediaModal, setOpenMediaModal] = useState(false);
  const [mediaData, setMediaData] = useState({});
  const [additionalFields, setAdditionalFields] = useState({});
  const [selectedTemplate, setSelectedTemplate] = useState({});
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownModalIdx, setDropdownModalIdx] = useState(-1);
  const [view, setView] = useState('items');
  const [indents, setIndents] = useState([]);
  const [indentDeliveryDate, setIndentDeliveryDate] = useState(
    new Date().toISOString().split('T')[0]
  );
  const [storeRequestDeliveryDate, setStoreRequestDeliveryDate] = useState(
    new Date().toISOString().split('T')[0]
  );
  const [storeRequest, setStoreRequests] = useState([]);

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'workOrderId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
  });

  // ); //CHANGE THE PATH FROM STATIC STRING TO PATH VARIBALE BEFORE RELEASE
  const { data: allParts = [] } = useGetAllPartsForOptionsQuery();
  const { data: allProducts = [] } = useGetAllProductsForOptionsQuery();
  const { data: templatesData } = useQueryTemplateByIdQuery({
    path: '/jobs/workorder',
  });
  const { data: salesData } = useGetSalesOrderOptionsQuery();

  const { data: assemblyForms = [] } = useGetAssemblyFormsQuery(
    {},
    { refetchOnMountOrArgChange: true }
  );
  const [createWorkOrder, { isLoading: isCreateLoading }] =
    useCreatePoV2Mutation();
  const [editWorkOrder, { isLoading: isEditLoading }] =
    useEditWorkOrderMutation();
  // const [updateDefaults] = useUpdateDefaultsMutation();
  const [getSalesOrderById] = useLazyGetSalesOrderByIdQuery();
  const [getMediaById] = useLazyGetMediaByIdQuery();
  const [getMediaArray] = useGetMediaByIdArrayMutation();
  const [getOrderDetailsByID] = useLazyGetOrderDetailsByIDQuery();

  useEffect(() => {
    if (editRecord?.length === 24) {
      let record = allData?.find((elem) => elem?._id === editRecord);
      setAttachments(record?.attachments);
      setItemsData(record?.items);
      setSelectedTemplate(record?.additionalFields);
      setAdditionalFields(record?.additionalFields);
      // delete record?.attachments;
      // delete record?.items;
      const isoString = record?.deadline;
      const date = new Date(isoString);
      const formatted = date.toISOString().split('T')[0];
      if (isCopy) {
        let date = new Date();
        setInputData({
          ...record,
          deadline: formatted,
          name: `${record?.name}-Copy-${date?.getTime()}`,
        });
      } else {
        setInputData({ ...record, deadline: formatted });
      }
    }
  }, [editRecord]); //eslint-disable-line

  useEffect(() => {
    if (templatesData) {
      let chosenTemplate = templatesData?.[0];
      setSelectedTemplate(chosenTemplate);
      setAdditionalFields(chosenTemplate);
      setInputData((prev) => ({
        ...prev,
        template: chosenTemplate?._id,
      }));
    }
  }, [templatesData]);

  const isEmptyObject = (obj) =>
    obj && typeof obj === 'object' && Object.keys(obj).length === 0;

  function cleanHtmlString(htmlString) {
    // Decode HTML entities
    let decodedString = htmlString.replace(/&lt;/g, '<').replace(/&gt;/g, '>');

    // Remove all HTML tags
    let plainText = decodedString.replace(/<\/?[^>]+(>|$)/g, '');

    // Trim and remove excessive whitespace
    return plainText.replace(/\s+/g, ' ').trim();
  }

  useEffect(() => {
    const getSalesOrder = async () => {
      if (inputData?.salesOrder?.length > 16) {
        let res = await getSalesOrderById({ id: inputData?.salesOrder });
        let soProducts = [];
        let temp2 = res?.data?.products?.filter(
          (elem) => elem?.value === undefined
        );
        let temp20 = res?.data?.products?.filter(
          (elem) => elem?.value !== undefined
        );
        let temp3 = temp2?.map((elem) => {
          let item = allProducts?.find(
            (el) => el?.name === cleanHtmlString(elem?.details)
          );
          return {
            ...elem,
            value: item?.value,
          };
        });
        let prods = [...temp20, ...temp3];

        for (let item of prods) {
          let attachment = '';
          if (item?.attachments?.length > 0) {
            const media = await getMediaById({
              id: item?.attachments?.[0],
            });
            attachment = media?.data?.media;
          }
          soProducts?.push({
            category: 'inhouse',
            itemId: item?.value,
            itemType: 'Product',
            attachments: [],
            inStock: 0,
            itemName: '',
            requiredStock: 0,
            units: item?.quantity || '',
            vendors: '',
            key: Array.from({ length: 16 }, () =>
              Math.floor(Math.random() * 10)
            ).join(''),
            soData: {
              uom: item?.UOM,
              attachments: attachment,
              cgst: item?.cgst,
              igst: item?.igst,
              sgst: item?.sgst,
              rate: item?.rate,
              amount: item?.amount,
              hsn: item?.hsn,
              discount: item?.discount,
              quantity: item?.quantity,
            },
          });
        }

        setItemsData(soProducts);
      }
    };
    if (
      editRecord?.length < 16 ||
      editRecord === undefined ||
      isEmptyObject(editRecord)
    ) {
      getSalesOrder();
    }
  }, [inputData?.salesOrder, editRecord]); //eslint-disable-line

  useEffect(() => {
    const templateFieldsFromSo = async () => {
      if (inputData?.salesOrder?.length > 16) {
        let res = await getSalesOrderById({ id: inputData?.salesOrder });
        let templateData = [];
        if (
          selectedTemplate?.templateData?.length > 0 &&
          res?.data?.additionalFields?.templateData?.length > 0
        ) {
          for (let i of selectedTemplate?.templateData) {
            let found = false;
            for (let j of res?.data?.additionalFields?.templateData) {
              if (
                j?.fieldName === i?.fieldName &&
                j?.fieldType === i?.fieldType
              ) {
                templateData?.push(j);
                found = true;
                break;
              }
            }
            if (!found) {
              templateData?.push(i);
            }
          }
          setAdditionalFields((prev) => ({
            ...prev,
            templateData,
          }));
        }
      }
    };
    if (
      editRecord?.length < 16 ||
      editRecord === undefined ||
      isEmptyObject(editRecord)
    ) {
      templateFieldsFromSo();
    }
  }, [inputData?.salesOrder, editRecord, selectedTemplate]); //eslint-disable-line

  const handleViewChange = (e) => {
    setView(e.target.value);
  };

  const attachmentChangeHandler = (e) => {
    for (let i in e) {
      let fname = e[i].name; // Get file name
      let ftype = e[i].type; // Get file type

      const fr = new FileReader(); // Create a new FileReader instance
      if (i === 'length') return; // Skip if 'length' property
      fr.readAsDataURL(e[i]); // Read the file as a data URL
      fr.addEventListener('load', () => {
        const url = fr.result; // Get the file data as a base64 URL
        let data = {
          name: fname,
          type: ftype,
          data: url,
        };
        setAttachments((prev) => [...prev, data]); // Update the pdf state with the new file data
      });
    }
  };

  const handleDeleteMedia = (_, idx) => {
    setAttachments((prev) => [...prev?.slice(0, idx), ...prev?.slice(idx + 1)]);
    setMediaData((prev) => ({
      ...prev,
      media: [...prev?.media?.slice(0, idx), ...prev?.media?.slice(idx + 1)],
    }));
  };

  const changeHandler = useCallback(
    (name, val) => {
      if (name === 'template') {
        let chosenTemplate = templatesData?.find((elem) => elem?._id === val);
        setSelectedTemplate(chosenTemplate);
        setAdditionalFields(chosenTemplate);
      }
      setInputData((prev) => ({
        ...prev,
        [name]: val,
      }));
      if (name === 'salesOrder') {
        setInputData((prev) => ({
          ...prev,
          orderQuantity: 1,
        }));
      }
    },
    [templatesData]
  );

  useEffect(() => {
    const fetchOrderDetails = async (orderId) => {
      const { data } = await getOrderDetailsByID(orderId);
      if (data?.currentPage?.[0] === 'Work Order') {
        const salesOrderStep = data.steps?.find(
          (i) => i.refKey === 'SalesOrder'
        );
        if (salesOrderStep && changeHandler) {
          changeHandler('salesOrder', salesOrderStep?.data?._id);
        }
      }
    };

    if (Searchparams.get('kanban') === 'true') {
      const orderId = Searchparams.get('orderId');
      if (orderId) {
        fetchOrderDetails(orderId);
      }
    }
  }, [getOrderDetailsByID, Searchparams, changeHandler]);

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = updatedTemplateData.findIndex(
          (template) => template.fieldType === 'table'
        );
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }
    if (fieldValue === '+') {
      setTemplateDropDownModal(true);
      const x = additionalFields?.templateData?.findIndex(
        (field) => field.fieldName === fieldName
      );
      setDropdownModalIdx(x);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field) => {
          if (field?.fieldName === fieldName) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );
      setAdditionalFields((prev) => {
        return {
          ...prev,
          templateData: updatedAdditionalFields,
        };
      });
    }
  };

  // console.log(itemsData, 'itemsData');

  const submitHandler = async () => {
    if (inputData?.name?.length === 0 || inputData?.name === undefined) {
      toast.error('Name is Required');
      return;
    }
    if (itemsData?.length === 0) {
      toast.error('At least one item is Required');
      return;
    }
    if (
      inputData?.orderQuantity <= 0 ||
      inputData?.orderQuantity === undefined
    ) {
      toast.error('Order Quantity is Required');
      return;
    }
    let user = JSON.parse(localStorage.getItem('user'))?.user;

    let indentTemp = indents?.map((elem) => {
      let quantity = elem?.orderQuantity;
      delete elem?.orderQuantity;
      return {
        ...elem,
        quantity,
      };
    });

    let indentsToBeCreated = {
      delivery_date: indentDeliveryDate,
      department: user?.name,
      requested_by: user?.name,
      remark: '',
      products: indentTemp,
      indent_no: Math.floor(
        1000000000000000 + Math.random() * 9000000000000000
      ),
    };

    let storeRequestTemp = storeRequest?.map((elem) => ({
      ...elem,
      delivery_date: indentDeliveryDate,
    }));

    let temp = itemsData;
    for (let i of temp) {
      delete i?.inStock;
      delete i?.requiredStock;
      delete i?.key;
      delete i?.itemName;
      delete i?.manualEntryStatus;
      if (i?.itemId?.length < 24) {
        delete i?.itemId;
        delete i?.itemType;
      }
      if (i?.vendor?.length === 0) {
        delete i?.vendor;
      }
    }

    let workOrder = {
      ...inputData,
      idData: idCompData?.dataToReturn,
      additionalFields,
      items: temp,
      indentsToBeCreated,
      storeRequestToBeRaised: storeRequestTemp,
      attachments,
    };
    let res;
    if (editRecord?.length === 24 && !isCopy) {
      res = await editWorkOrder({ id: editRecord, data: workOrder });
      if (res?.data?._id !== undefined) {
        // await updateDefaults(newDefaults);
        toast.success('Work Order Edited');
      } else {
        toast.error(`${res?.data?.createdPo?.error}`);
        return;
      }
    } else {
      if (isCopy) {
        delete workOrder?._id;
        delete workOrder?._v;
        delete workOrder?.workOrderId;
        delete workOrder?.storeRequests;
        delete workOrder?.indents;
        delete workOrder?.taskId;
        delete workOrder?.createdAt;
        workOrder.indentsApproved = false;
        workOrder.storeRequestApproved = false;
        let items = workOrder?.items;
        let arr = [];
        for (let i of items) {
          let temp = { ...i };
          if (temp?.createInput) {
            delete temp?.createInput;
          }
          temp.jobCreated = false;
          arr?.push(temp);
        }
        workOrder.items = arr;
      }
      res = await createWorkOrder({ data: workOrder });
      if (res?.data?.createdPo?._id !== undefined) {
        // await updateDefaults(newDefaults);
        toast.success('Work Order Created');
      } else {
        toast.error(`${res?.data?.createdPo?.error}`);
        return;
      }
    }
    const kanban = Searchparams.get('kanban') === 'true';
    const orderId = Searchparams.get('orderId');
    const navigateParams = {
      department: Searchparams.get('department'),
      id: res?.data?.createdPo?._id,
      refType: Searchparams.get('refType'),
      page: Searchparams.get('page'),
      taskId: Searchparams.get('taskId'),
      orderId,
      index: Searchparams.get('index'),
      idIndex: idCompData?.dataToReturn?.idIndex,
    };

    if (!kanban) {
      let obj = {
        objRef: res?.data?.createdPo?._id,
        currentDepartment: 'production',
        refKey: 'WorkOrder',
        currentPage: 'Work Order',
        userId: state?.user?._id,
        taskId: generatePrefixId(defaultParam?.prefixIds?.['taskId']),
      };
      await createDepOrder({
        data: obj,
      });
    }
    if (res && kanban) {
      let time = new Date();
      dispatch({
        type: 'ADD_CARD',
        payload: {
          data: {
            taskId: Searchparams.get('taskId'),
            stepPage: 'Work Order',
            updatedAt: time?.toDateString(),
          },
          currentColumn: 'Work Order',
        },
      });
    }
    const filteredParams = Object.fromEntries(
      Object.entries(navigateParams).filter(([_, value]) => value !== null)
    );

    const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;
    if (kanban) {
      navigate(navigateStr);
    } else {
      closeForm();
    }
  };

  const exitForm = () => {
    setInputData({
      template: '',
      name: '',
      additionalFields: {},
    });
    setAttachments([]);
    setItemsData([]);
    setSelectedTemplate('');
    setAdditionalFields({});
    setIndents([]);
    setStoreRequests([]);
    closeForm();
  };

  return (
    <>
      <MediaPreviewModal
        openMediaModal={openMediaModal}
        setOpenMediaModal={setOpenMediaModal}
        mediaData={mediaData}
        setMediaData={setMediaData}
        handleDeleteMedia={handleDeleteMedia}
        fieldName={mediaData?.fieldName}
      />
      <div className="bg-white rounded-md px-8 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-gray-800">Create Work Order</h1>
          <AntdButton onClick={exitForm}>Back</AntdButton>
        </div>
        <div className="grid grid-cols-3 mt-4 gap-2">
          <div>
            <Label>Template</Label>
            <Select
              options={templatesData?.map((template) => ({
                value: template._id,
                label: template.name,
              }))}
              onChange={(e) => changeHandler('template', e.target.value)}
              value={inputData?.template || inputData?.additionalFields?._id}
            />
          </div>
          <div>
            <Label>Deadline Date</Label>
            <Input
              type="date"
              onChange={(e) => changeHandler('deadline', e.target.value)}
              value={inputData?.deadline}
              inputClassname="bg-white"
            />
          </div>
          <div>
            <Label>Sales Order</Label>
            <Select
              options={salesData?.map((elem) => ({
                label: `${elem?.salesOrderID} - ${
                  elem?.CustomerData?.companyName || ''
                }`,
                value: elem?._id,
              }))}
              onChange={(e) => changeHandler('salesOrder', e.target.value)}
              value={inputData?.salesOrder}
            />
          </div>
        </div>
        <div className="grid grid-cols-2 mt-4 gap-2">
          <div>
            <Label>Work Order ID</Label>
            {editRecord?.length >= 24 && !isCopy ? (
              <p>{inputData?.workOrderId}</p>
            ) : (
              <IdGenComp {...idCompData} />
            )}
          </div>
          <div>
            <Label>Name</Label>
            <Input
              onChange={(e) => {
                let val = e.target.value;
                changeHandler('name', val);
              }}
              value={inputData?.name}
              inputClassname="bg-white"
            />
          </div>
        </div>
        <div>
          {additionalFields?.templateData?.length > 0 && (
            <Label>Template Fields</Label>
          )}
          <div className="w-full">
            {renderFieldsBasedOnType(
              additionalFields,
              handleInputChange,
              templateDropDownModal,
              setTemplateDropDownModal,
              setAdditionalFields,
              newOptionStatus,
              setNewOptionStatus,
              dropdownModalIdx,
              setDropdownModalIdx,
              Searchparams
            )}
          </div>
        </div>
        <div className="grid grid-cols-2 gap-x-4 mt-4 gap-y-2">
          <div className="w-full justify-self-end relative">
            <div className="flex gap-x-2 items-center justify-between mb-3">
              <label className="block mb-1 text-sm text-gray-500 font-medium">
                Upload Files
              </label>
              {attachments?.length > 0 && (
                <Button
                  color="blue"
                  size="sm"
                  variant="solid"
                  onClick={async () => {
                    let idAttachments = attachments?.filter(
                      (elem) => elem?.data === undefined
                    );
                    let newAttachments = attachments?.filter(
                      (elem) => elem?.data !== undefined
                    );
                    let media = await getMediaArray({
                      data: { ids: idAttachments },
                    });
                    let mediaData = media?.data?.media?.map((elem) => ({
                      name: elem?.name,
                      type: elem?.type,
                      data: elem?.data,
                    }));
                    mediaData = [...mediaData, ...newAttachments];
                    setMediaData({
                      media: mediaData,
                    });
                    setOpenMediaModal(true);
                  }}
                >
                  Preview Files
                </Button>
              )}
            </div>
            <div className="w-full relative">
              <UploadButton
                type="file"
                width={'w-full'}
                accept={'image/*'}
                fileType="JPG/PNG"
                multiple={true}
                onChange={attachmentChangeHandler}
              />
              {attachments?.length > 0 && (
                <span className="text-white bg-red-500 rounded-full w-5 h-5 absolute top-[-0.5rem] right-[-0.5rem] text-nowrap text-center text-xs items-center justify-center  flex ">
                  {attachments?.length}
                </span>
              )}
            </div>
          </div>
          <div>
            <Label>Work Order SOP</Label>
            <Select
              options={assemblyForms?.map((elem) => ({
                label: elem?.formName,
                value: elem?._id,
              }))}
              className="mt-4"
              onChange={(e) => changeHandler('sop', e.target.value)}
              value={inputData?.sop}
            />
          </div>
        </div>
        <Radio.Group onChange={handleViewChange} value={view} className="mt-4">
          <Radio.Button value="items">Items</Radio.Button>
          <Radio.Button value="actions">Actions</Radio.Button>
        </Radio.Group>
        <div className="mt-2">
          {view === 'items' && (
            <div className="flex items-center justify-between">
              <Label>Items</Label>
              <div className="flex items-center gap-2 justify-end mb-2">
                <Label>Order Quantity: </Label>
                <Input
                  onChange={(e) =>
                    changeHandler(
                      'orderQuantity',
                      e.target.value.replace(/\s/g, '')
                    )
                  }
                  value={inputData?.orderQuantity}
                  inputClassname="bg-white h-[2.1em] text-center text-sm"
                  placeholder="Enter Order Quantity"
                />
                <AntdButton
                  type="primary"
                  onClick={() => {
                    setItemsData((prev) => [
                      ...prev,
                      {
                        key: Array.from({ length: 16 }, () =>
                          Math.floor(Math.random() * 10)
                        ).join(''),
                        category: '',
                        itemName: '',
                        itemId: '',
                        units: '',
                        inStock: 0,
                        requiredStock: 0,
                        vendor: '',
                        attachments: [],
                      },
                    ]);
                  }}
                >
                  + Add Item
                </AntdButton>
              </div>
            </div>
          )}
          {view === 'items' && (
            <CreateWorkOrderItemsTable
              rows={itemsData}
              setRows={setItemsData}
              orderQuantity={inputData?.orderQuantity}
              setIndents={setIndents}
              setStoreRequests={setStoreRequests}
              allParts={allParts}
              allProducts={allProducts}
            />
          )}
          {view === 'actions' && (
            <WorkOrderActions
              indents={indents}
              storeRequests={storeRequest}
              setIndents={setIndents}
              setIndentDeliveryDate={setIndentDeliveryDate}
              indentDeliveryDate={indentDeliveryDate}
              storeRequestDeliveryDate={storeRequestDeliveryDate}
              setStoreRequestDeliveryDate={setStoreRequestDeliveryDate}
            />
          )}
        </div>
        <Button
          onClick={submitHandler}
          className="mt-2 ml-auto"
          disabled={isCreateLoading || isEditLoading ? true : false}
        >
          Submit
        </Button>
      </div>
    </>
  );
};

export default CreateWorkOrderV2;
