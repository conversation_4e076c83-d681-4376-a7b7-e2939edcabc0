import { useEffect, useState } from 'react';
import { FormatDate, generateDateString } from '../../helperFunction';
import Table from '../global/components/Table';
// import PoSidebarData from '../PurchaseOrder/PoSidebarData';
// import MediaDetails from '../v3/global/components/MediaDetails';
import SalesOrderSideBar from '../salesOrder/salesOrderSideBar';
import QuotationRightSidebar from '../SalesOrderManagement/Quotation/QuotationRightSidebar';
import PODashboardSidebar from '../po-dashboard/po-dashboard-sidebar';

const ApprovalSidebarData = ({
  data,
  tab,
  // setReadMore,
  // setShowSidebar,
  showSidebar,
  setMedia,
}) => {
  const [_total, setTotal] = useState();

  const calculateTaxAmount = (amount, percentage) =>
    (amount * (percentage || 0)) / 100;

  const _calculateTotalAmount = (item) => {
    const sgst = calculateTaxAmount(item?.amount, item?.sgst);
    const cgst = calculateTaxAmount(item?.amount, item?.cgst);
    const igst = calculateTaxAmount(item?.amount, item?.igst);
    const totalAmount = item?.amount + sgst + cgst + igst;
    return totalAmount;
  };

  useEffect(() => {
    if (tab !== 'salesQuotations') return;
    let sum = 0;
    data?.productDetails?.map((item) => {
      sum += item?.totalAmount || 0;
    });
    setTotal(sum);
  }, [data, tab]);

  useEffect(() => {
    if (tab === 'salesOrders') {
      setMedia(data?.files);
    } else if (tab === 'salesQuotations') {
      setMedia(data?.attachments);
    }
  }, [data, tab, setMedia]);

  const renderDetails = (details) => {
    if (tab === 'purchaseIndents') {
      return (
        <>
          <Table>
            <Table.Head>
              <Table.Row>
                <Table.Th>Indent Details</Table.Th>
                <Table.Th></Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              <Table.Row>
                <Table.Td>Indent No</Table.Td>
                <Table.Td>{details?.indent_no}</Table.Td>
              </Table.Row>
              <Table.Row>
                <Table.Td>Product Name</Table.Td>
                <Table.Td>{details?.product_name}</Table.Td>
              </Table.Row>
              <Table.Row>
                <Table.Td>UOM</Table.Td>
                <Table.Td>{details?.uom}</Table.Td>
              </Table.Row>
              <Table.Row>
                <Table.Td>Quantity</Table.Td>
                <Table.Td>{details?.quantity}</Table.Td>
              </Table.Row>
              <Table.Row>
                <Table.Td>Vendor</Table.Td>
                <Table.Td>{details?.vendor_name}</Table.Td>
              </Table.Row>
              <Table.Row>
                <Table.Td>Delivery Date</Table.Td>
                <Table.Td>{FormatDate(details?.delivery_date)}</Table.Td>
              </Table.Row>
              <Table.Row>
                <Table.Td>Department</Table.Td>
                <Table.Td>{details?.department}</Table.Td>
              </Table.Row>
              <Table.Row>
                <Table.Td>Requested By</Table.Td>
                <Table.Td>{details?.request_by}</Table.Td>
              </Table.Row>
            </Table.Body>
          </Table>
        </>
      );
    } else if (tab === 'purchaseOrders') {
      return (
        <>
          <PODashboardSidebar
            fromKanban={true}
            vendorData={[data?.vendor]}
            selectedPo={data}
          />
        </>
      );
    } else if (tab === 'salesOrders') {
      return (
        <>
          <SalesOrderSideBar fromKanban={true} data={[data]} pos={0} />
        </>
      );
    } else {
      return (
        <>
          <QuotationRightSidebar
            data={data}
            disabled={true}
            generateDateString={generateDateString}
            openSideBar={showSidebar}
            fromKanban={true}
          />
        </>
      );
    }
  };
  return <section>{renderDetails(data)}</section>;
};

export default ApprovalSidebarData;
