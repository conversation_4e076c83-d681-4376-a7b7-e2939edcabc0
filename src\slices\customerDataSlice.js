import { apiSlice } from './apiSlice';

export const CustomerDataSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    createColumn: builder.mutation({
      query: (body) => ({ url: '/v1/customer/column', method: 'POST', body }),
      invalidatesTags: ['CustomerData'],
    }),
    getAllcolumns: builder.query({
      query: () => ({ url: '/v1/customer/column' }),
      providesTags: ['CustomerData'],
    }),
    createValue: builder.mutation({
      query: (body) => ({ url: '/v1/customer/value', method: 'POST', body }),
      invalidatesTags: ['ColumnsValue', 'QueryAllCustomers'],
    }),
    getAllvalue: builder.query({
      query: () => ({ url: '/v1/customer/value', method: 'GET' }),
      providesTags: ['ColumnsValue'],
    }),
    getCustomervalue: builder.query({
      query: (id) => ({ url: `/v1/customer/${id}`, method: 'GET' }),
    }),
    createCustomer: builder.mutation({
      query: (body) => ({
        url: `/v1/customer?showErr=${body?.error || 'true'}`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['NewCustomers', 'QueryAllCustomers', 'PrefixIds'],
    }),
    getAllcustomer: builder.query({
      query: () => ({ url: '/v1/customer', method: 'GET' }),
      providesTags: ['NewCustomers', 'QueryAllCustomers'],
    }),

    editCustomer: builder.mutation({
      query: (argv) => ({
        url: `/v1/customer/${argv.id}`,
        method: 'PUT',
        body: argv.body,
      }),
      invalidatesTags: ['NewCustomers', 'QueryAllCustomers'],
    }),

    addManyCustomer: builder.mutation({
      query: (data) => ({
        url: `/v1/customer/addManyCustomer`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['NewCustomers', 'QueryAllCustomers'],
    }),

    deleteCustomer: builder.mutation({
      query: (id) => ({ url: `/v1/customer/${id}`, method: 'DELETE' }),
      invalidatesTags: ['QueryAllCustomers'],
    }),
    deleteManyCustomer: builder.mutation({
      query: ({ ids }) => ({
        url: `/v1/customer/`,
        method: 'DELETE',
        body: { ids },
      }),
      invalidatesTags: ['QueryAllCustomers'],
    }),
    queryCustomers: builder.query({
      query: ({
        page,
        limit,
        fieldName,
        fieldvalue,
        debounceSearch,
        userType,
      }) => ({
        url: `/v1/customer/query?page=${page}&limit=${limit}&fieldName=${fieldName}&fieldvalue=${fieldvalue}&searchTerm=${debounceSearch}&userType=${userType}`,
        method: 'GET',
      }),
      providesTags: ['QueryAllCustomers'],
    }),
    deleteColumn: builder.mutation({
      query: (id) => ({ url: `/v1/customer/column/${id}`, method: 'DELETE' }),
      invalidatesTags: ['CustomerData'],
    }),
    getCustomerById: builder.query({
      query: (id) => ({
        url: `/v1/customer/get-customer/${id}`,
        method: 'GET',
      }),
    }),
    getLatestCustomer: builder.query({
      query: () => ({
        url: `/v1/customer/latest`,
        method: 'GET',
      }),
    }),
    getCustomerOptions: builder.query({
      query: () => ({
        url: `/v1/customer/options`,
        method: 'GET',
      }),
      providesTags: ['CustomerData', 'NewCustomers'],
    }),
  }),
});

export const {
  useCreateColumnMutation,
  useLazyGetAllcolumnsQuery,
  useCreateValueMutation,
  useLazyGetAllvalueQuery,
  useLazyGetCustomervalueQuery,
  useCreateCustomerMutation,
  useLazyGetAllcustomerQuery,
  useDeleteCustomerMutation,
  useEditCustomerMutation,
  useGetAllcustomerQuery,
  useAddManyCustomerMutation,
  useDeleteManyCustomerMutation,
  useDeleteColumnMutation,
  useLazyQueryCustomersQuery,
  useGetCustomerByIdQuery,
  useGetLatestCustomerQuery,
  useGetCustomerOptionsQuery,
} = CustomerDataSlice;
