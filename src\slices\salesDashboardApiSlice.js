import { apiSlice } from './apiSlice';

const Base_Route = '/v1/sales-dashboard';

export const salesDashboardApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // ===== QUOTATIONS QUERIES =====
    getQuotationsMetrics: builder.query({
      query: ({ dateRange, customerId } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        if (customerId) params.append('customerId', customerId);
        return `${Base_Route}/quotations/metrics?${params}`;
      },
      providesTags: ['QuotationsMetrics'],
    }),

    getQuotationsTrend: builder.query({
      query: ({ dateRange, customerId } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        if (customerId) params.append('customerId', customerId);
        return `${Base_Route}/quotations/trend?${params}`;
      },
      providesTags: ['QuotationsTrend'],
    }),

    getQuotationsTopCustomers: builder.query({
      query: ({ dateRange, limit = 5, topCustomerFilter } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        params.append('limit', limit);
        if (topCustomerFilter?.by) params.append('by', topCustomerFilter.by);
        if (topCustomerFilter?.order)
          params.append('order', topCustomerFilter.order);
        return `${Base_Route}/quotations/top-customers?${params}`;
      },
      providesTags: ['QuotationsTopCustomers'],
    }),
    getQuotationsTopProducts: builder.query({
      query: ({ dateRange, limit = 5 } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        params.append('limit', limit);
        return `${Base_Route}/quotations/top-products?${params}`;
      },
      providesTags: ['QuotationsTopProducts'],
    }),

    getQuotationsFormats: builder.query({
      query: ({ dateRange, customerId } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        if (customerId) params.append('customerId', customerId);
        return `${Base_Route}/quotations/formats?${params}`;
      },
      providesTags: ['QuotationsFormats'],
    }),

    getQuotationFormatDetails: builder.query({
      query: ({ formatId, dateRange, customerId } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        if (customerId) params.append('customerId', customerId);

        return `${Base_Route}/quotations/formats/${formatId}/details?${params}`;
      },
      providesTags: ['QuotationFormatDetails'],
    }),
    getQuotationToSalesOrderConversion: builder.query({
      query: ({ dateRange, customerId } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        if (customerId) params.append('customerId', customerId);
        return `${Base_Route}/quotations/conversion?${params}`;
      },
      providesTags: ['QuotationConversion'],
    }),
    GetQuotationsCreatedByData: builder.query({
      query: ({ dateRange } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        return `${Base_Route}/quotations/created-by?${params}`;
      },
      providesTags: ['QuotationCreatedBy'],
    }),

    // ===== SALES ORDERS QUERIES =====
    getSalesOrdersMetrics: builder.query({
      query: ({ dateRange, customerId } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        if (customerId) params.append('customerId', customerId);
        return `${Base_Route}/sales-orders/metrics?${params}`;
      },
      providesTags: ['SalesOrdersMetrics'],
    }),

    getSalesOrdersTrend: builder.query({
      query: ({ dateRange, customerId } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        if (customerId) params.append('customerId', customerId);
        return `${Base_Route}/sales-orders/trend?${params}`;
      },
      providesTags: ['SalesOrdersTrend'],
    }),
    getSalesOrdersPerformance: builder.query({
      query: ({ dateRange, customerId } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        if (customerId) params.append('customerId', customerId);
        return `${Base_Route}/sales-orders/performance?${params}`;
      },
      providesTags: ['SalesOrdersPerformance'],
    }),

    getSalesOrdersTopCustomers: builder.query({
      query: ({ dateRange, limit = 5, topCustomerFilter } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        params.append('limit', limit);
        if (topCustomerFilter?.by) params.append('by', topCustomerFilter.by);
        if (topCustomerFilter?.order)
          params.append('order', topCustomerFilter.order);
        return `${Base_Route}/sales-orders/top-customers?${params}`;
      },
      providesTags: ['SalesOrdersTopCustomers'],
    }),
    getSalesOrdersTopProducts: builder.query({
      query: ({ dateRange, limit = 5 } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        params.append('limit', limit);
        return `${Base_Route}/sales-orders/top-products?${params}`;
      },
      providesTags: ['SalesOrdersTopProducts'],
    }),

    getSalesOrdersFormats: builder.query({
      query: ({ dateRange, customerId } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        if (customerId) params.append('customerId', customerId);
        return `${Base_Route}/sales-orders/formats?${params}`;
      },
      providesTags: ['SalesOrdersFormats'],
    }),

    getSalesOrderFormatDetails: builder.query({
      query: ({ formatId, dateRange, customerId } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        if (customerId) params.append('customerId', customerId);

        return `${Base_Route}/sales-orders/formats/${formatId}/details?${params}`;
      },
      providesTags: ['SalesOrderFormatDetails'],
    }),
    GetSalesOrdersCreatedByData: builder.query({
      query: ({ dateRange } = {}) => {
        const params = new URLSearchParams();
        if (dateRange?.startDate)
          params.append('startDate', dateRange.startDate);
        if (dateRange?.endDate) params.append('endDate', dateRange.endDate);
        return `${Base_Route}/sales-orders/created-by?${params}`;
      },
      providesTags: ['SalesOrderCreatedBy'],
    }),

    // ===== DASHBOARD PREFERENCES =====
    saveDashboardPreferences: builder.mutation({
      query: ({ tabType, preferences }) => ({
        url: `${Base_Route}/preferences/${tabType}`,
        method: 'POST',
        body: {
          selectedMetrics: preferences.selectedMetrics || [],
          dateRange: preferences.dateRange || {
            startDate: null,
            endDate: null,
          },
          selectedCustomer: preferences.selectedCustomer || null,
        },
      }),
      invalidatesTags: ['DashboardPreferences'],
    }),

    getDashboardPreferences: builder.query({
      query: ({ tabType }) => `${Base_Route}/preferences/${tabType}`,
      providesTags: ['DashboardPreferences'],
      transformResponse: (response) => {
        return {
          selectedMetrics: response?.selectedMetrics || [],
          dateRange: response?.dateRange || { startDate: null, endDate: null },
          selectedCustomer: response?.selectedCustomer || null,
        };
      },
    }),
  }),
});

export const {
  // Quotations hooks
  useGetQuotationsMetricsQuery,
  useLazyGetQuotationsMetricsQuery,
  useGetQuotationsTrendQuery,
  useLazyGetQuotationsTrendQuery,
  useGetQuotationsTopCustomersQuery,
  useLazyGetQuotationsTopCustomersQuery,
  useGetQuotationsTopProductsQuery,
  useLazyGetQuotationsTopProductsQuery,
  useGetQuotationsFormatsQuery,
  useLazyGetQuotationsFormatsQuery,
  useGetQuotationFormatDetailsQuery,
  useLazyGetQuotationFormatDetailsQuery,
  useGetQuotationToSalesOrderConversionQuery,
  useLazyGetQuotationToSalesOrderConversionQuery,
  useGetQuotationsCreatedByDataQuery,
  useLazyGetQuotationsCreatedByDataQuery,

  // Sales Orders hooks
  useGetSalesOrdersMetricsQuery,
  useLazyGetSalesOrdersMetricsQuery,
  useGetSalesOrdersTrendQuery,
  useLazyGetSalesOrdersTrendQuery,
  useGetSalesOrdersPerformanceQuery,
  useLazyGetSalesOrdersPerformanceQuery,
  useGetSalesOrdersTopCustomersQuery,
  useLazyGetSalesOrdersTopCustomersQuery,
  useGetSalesOrdersTopProductsQuery,
  useLazyGetSalesOrdersTopProductsQuery,
  useGetSalesOrdersFormatsQuery,
  useLazyGetSalesOrdersFormatsQuery,
  useGetSalesOrderFormatDetailsQuery,
  useLazyGetSalesOrderFormatDetailsQuery,
  useGetSalesOrdersCreatedByDataQuery,
  useLazyGetSalesOrdersCreatedByDataQuery,

  // Dashboard Preferences hooks
  useSaveDashboardPreferencesMutation,
  useGetDashboardPreferencesQuery,
  useLazyGetDashboardPreferencesQuery,
} = salesDashboardApiSlice;
