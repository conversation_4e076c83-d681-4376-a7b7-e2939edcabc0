import moment from 'moment';
import { useContext, useEffect, useState } from 'react';
import { Store } from '../../../store/Store';
import { Popover } from 'antd';
import { IoInformationCircle } from 'react-icons/io5';
import {
  IdcardOutlined,
  ShoppingCartOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Button, Table, Tabs } from 'antd';
import { toast } from 'react-toastify';
import RightSidebar from '../../global/components/RightSidebar';
import ShowTemplateValues from '../global/components/ShowTemplateValues';
import AddJobDetailsModal from './AddJobDetailsModal';
import ItemModal from './ItemModal';
import {
  useCreateOutsourceJobsMutation,
  useGetWorkOrderByIdQuery,
  useRaiseIndentsMutation,
  useRaiseJobRequestsMutation,
  useRaiseStoreRequestsMutation,
} from '../../../slices/createPoApiSlice';
import { useGetAllPartsForOptionsQuery } from '../../../slices/partApiSlice';
import { useGetAllProductsForOptionsQuery } from '../../../slices/productApiSlice';
import Spinner from '../../global/components/Spinner';

const GeneralDetailsRow = ({ name, Icon, value, color, className }) => {
  return (
    <div
      className={`flex items-center justify-between border-b pb-2 ${className}`}
    >
      <div className="flex items-center gap-2">
        <Icon className={`${color} text-xl`} />
        <span className="font-medium text-gray-600">{name}</span>
      </div>
      <span className="text-gray-800 font-medium">{value}</span>
    </div>
  );
};

const WorkOrderSidebarV2 = ({
  fromKanban = false,
  selectedWorkOrder,
  openSideBar,
  setOpenWorkOrderSidebar,
  onCloseSidebar,
}) => {
  const { data: allParts = [] } = useGetAllPartsForOptionsQuery();
  const { data: allProducts = [] } = useGetAllProductsForOptionsQuery();
  const { defaults: defaultParam } = useContext(Store);

  const [bomToShow, setBomToShow] = useState({
    bom: {},
    open: false,
  });

  const [rmToShow, setRMToShow] = useState({
    rawMaterials: [],
    assets: [],
    orderQuantity: 0,
    open: false,
  });
  const [selectedTab, setSelectedTab] = useState('workOrder');

  const [selectedIndents, setSelectedIndents] = useState([]);
  const [selectedStoreRequests, setSelectedStoreRequests] = useState([]);
  const [selectedOutsourceJobRequests, setSelectedOutsourceJobRequests] =
    useState([]);

  // const [allJobData, setAllJobData] = useState([]);
  const [addJobDetails, setAddJobDetails] = useState({
    isOpen: false,
    id: null,
    template: null,
  });
  // const [jobSubmitStatus, setJobSubmitStatus] = useState(false);
  // const [DeadlineDate, setDeadlineDate] = useState('');
  const [tempDefaults, setTempDefaults] = useState(null);

  const { data: workOrder, isLoading: isWorkOrderLoading } =
    useGetWorkOrderByIdQuery(
      selectedWorkOrder?.length === 24
        ? { id: selectedWorkOrder }
        : { skip: true }
    );

  const [raiseIndents] = useRaiseIndentsMutation();
  const [raiseStoreRequests] = useRaiseStoreRequestsMutation();
  const [raiseJobRequests] = useRaiseJobRequestsMutation();
  const [createOutsourceJobs] = useCreateOutsourceJobsMutation();

  useEffect(() => {
    if (!tempDefaults?.defaultParam) {
      setTempDefaults(defaultParam);
    }
  }, [defaultParam, tempDefaults]);

  const closeBomModal = () => {
    setBomToShow({
      bom: {},
      open: false,
    });
  };

  const closeRMModal = () => {
    setRMToShow({
      rawMaterials: [],
      open: false,
    });
  };

  const itemColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',

      render: (_, record) => {
        const content = (
          <div className="border-t-[1px] border-slate-200 py-2 w-[450px]">
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Name: </h5>
              <p className="px-3">{record?.itemId?.name || '-'}</p>
            </div>
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Type: </h5>
              <p>{record?.itemType || '-'}</p>
            </div>
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Uom: </h5>
              <p>{record?.itemId?.uom || '-'}</p>
            </div>
          </div>
        );
        return (
          <div className="flex items-center gap-2 justify-items-start ">
            <span className="w-[50%]">
              {record?.itemId?.name?.length > 25
                ? `${record?.itemId?.name?.slice(0, 25)}...`
                : record?.itemId?.name}
            </span>
            <Popover
              content={content}
              title="Product Details"
              className="w-[45px]"
            >
              <IoInformationCircle className="text-xl text-blue-500" />
            </Popover>
          </div>
        );
      },
    },
    {
      title: 'Item Type',
      dataIndex: 'itemType',
      key: 'itemType',
      render: (_, record) => <span>{record?.itemType || '-'}</span>,
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (_, record) => <span>{record?.category}</span>,
    },
    {
      title: 'Units',
      dataIndex: 'unitsRequired',
      key: 'unitsRequired',
      render: (_, record) => <span>{record?.units}</span>,
    },
    {
      title: 'Required Stock',
      dataIndex: 'requiredStock',
      key: 'requiredStock',
      render: (_, record) => (
        <span>{record?.units * workOrder?.orderQuantity}</span>
      ),
    },
    {
      title: 'Additional Items',
      dataIndex: 'additionalItems',
      key: 'additionalItems',
      render: (_, record) => (
        <span
          className="text-blue-500 underline cursor-pointer hover:text-blue-300"
          onClick={() => {
            if (record?.bom?.item?.length > 0) {
              setBomToShow({
                bom: record?.bom,
                orderQuantity: record?.units * workOrder?.orderQuantity,
                open: true,
              });
            }
            if (record?.rawMaterials?.length > 0) {
              setRMToShow({
                rawMaterials: record?.rawMaterials || [],
                assets: record?.assetData || [],
                orderQuantity: record?.units,
                open: true,
              });
            }
          }}
        >
          {record?.bom?.item?.length > 0
            ? 'Show Bom'
            : record?.rawMaterials?.length > 0
              ? 'Show Raw Materials'
              : '-'}
        </span>
      ),
    },
  ];

  const indentAndStoreColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      // render: (_, record) => <span>{record?.name}</span>,
      render: (_, record) => {
        const content = (
          <div className="border-t-[1px] border-slate-200 py-2 w-[450px]">
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Name: </h5>
              <p className="px-3">{record?.name || '-'}</p>
            </div>
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Type: </h5>
              <p>{record?.type || '-'}</p>
            </div>
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Uom: </h5>
              <p>{record?.uom || '-'}</p>
            </div>
          </div>
        );
        return (
          <div className="flex   justify-items-start">
            <span className="w-[50%]">
              {record?.name?.length > 25
                ? `${record?.name?.slice(0, 25)}...`
                : record?.name}
            </span>
            <Popover
              content={content}
              title="Product Details"
              className="w-[45px]"
            >
              <IoInformationCircle className="text-xl text-blue-500 " />
            </Popover>
          </div>
        );
      },
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (_, record) => <span>{record?.type}</span>,
    },
    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
      render: (_, record) => <span>{record?.uom}</span>,
    },
    {
      title: 'Order Quantity',
      dataIndex: 'orderQuantity',
      key: 'orderQuantity',
      render: (_, record) => <span>{record?.quantity}</span>,
    },
  ];

  const indentsRaisedColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      // render: (_, record) => <span>{record?.product_name}</span>,

      render: (_, record) => {
        const content = (
          <div className="border-t-[1px] border-slate-200 py-2 w-[450px]">
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Name: </h5>
              <p className="px-3">{record?.product_name || '-'}</p>
            </div>
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Type: </h5>
              <p>{record?.type || '-'}</p>
            </div>
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Uom: </h5>
              <p>{record?.uom || '-'}</p>
            </div>
          </div>
        );
        return (
          <div className="flex   justify-items-start">
            <span className="w-[50%]">
              {record?.product_name?.length > 25
                ? `${record?.product_name?.slice(0, 25)}...`
                : record?.product_name}
            </span>
            <Popover
              content={content}
              title="Product Details"
              className="w-[45px]"
            >
              <IoInformationCircle className="text-xl text-blue-500 " />
            </Popover>
          </div>
        );
      },
    },
    {
      title: 'Delivery Date',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      render: (_, record) => (
        <span>{moment(record?.delivery_date).format('DD-MM-YYYY')}</span>
      ),
    },
    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
      render: (_, record) => <span>{record?.uom}</span>,
    },
    {
      title: 'Requested By',
      dataIndex: 'requestedBy',
      key: 'requestedBy',
      render: (_, record) => <span>{record?.request_by}</span>,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => (
        <span
          className={`py-[4px] px-4 rounded text-white text-[10px] ${record?.status === 'pending' ? 'bg-yellow-500' : 'bg-green-500'}`}
        >
          {record?.status?.toUpperCase()}
        </span>
      ),
    },
    {
      title: 'PO Status',
      dataIndex: 'poStatus',
      key: 'poStatus',
      render: (_, record) => (
        <span
          className={`py-[4px] px-4 rounded text-white text-[10px] ${record?.po === 'not created' ? 'bg-red-500' : 'bg-green-500'}`}
        >
          {record?.po?.toUpperCase()}
        </span>
      ),
    },
  ];

  const storeRequestRaisedColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',

      render: (_, record) => {
        const item = record?.product || record?.part || {};
        const content = (
          <div className="border-t-[1px] border-slate-200 py-2 w-[450px]">
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Name: </h5>
              <p className="px-3">{item?.name || '-'}</p>
            </div>
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Type: </h5>
              <p>{record?.item?.type || '-'}</p>
            </div>
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Uom: </h5>
              <p>{item?.uom || '-'}</p>
            </div>
          </div>
        );
        return (
          <div className="flex   justify-items-start">
            <span className="w-[50%]">
              {item?.name?.length > 25
                ? `${item?.name?.slice(0, 25)}...`
                : item?.name}
            </span>
            <Popover
              content={content}
              title="Product Details"
              className="w-[45px]"
            >
              <IoInformationCircle className="text-xl text-blue-500 " />
            </Popover>
          </div>
        );
      },
    },

    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
      render: (_, record) => (
        <span>{record?.part?.uom || record?.product?.uom}</span>
      ),
    },

    {
      title: 'Order Quantity',
      dataIndex: 'orderQuantity',
      key: 'orderQuantity',
      render: (_, record) => <span>{record?.stockOutQty}</span>,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => {
        let statusText = record?.status?.toUpperCase();
        let bgColor = 'bg-green-500';
        if (record?.status === 'pending') {
          bgColor = 'bg-yellow-500';
        } else if (record?.status === 'partially completed') {
          bgColor = 'bg-blue-500';
        }
        return (
          <span
            className={`py-[4px] px-4 rounded text-white text-[10px] ${bgColor}`}
          >
            {statusText}
          </span>
        );
      },
    },
  ];

  const jobRequestColumns = [
    {
      title: 'Item',
      dataIndex: 'item',
      key: 'item',
      // render: (_, record) => <span>{record?.itemId?.name}</span>,
      render: (_, record) => {
        let item = record?.itemId || record?.item || {};
        const content = (
          <div className="border-t-[1px] border-slate-200 py-2 w-[450px]">
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Name: </h5>
              <p className="px-3">{item?.name || '-'}</p>
            </div>
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Type: </h5>
              <p>{record?.itemType || '-'}</p>
            </div>
            <div className="flex items-center gap-2 justify-between">
              <h5 className="text-slate-600">Uom: </h5>
              <p>{item?.uom || '-'}</p>
            </div>
          </div>
        );
        return (
          <div className="flex items-center gap-2 justify-items-start">
            <span className="w-[50%]">
              {item.name?.length > 25
                ? `${item?.name?.slice(0, 25)}...`
                : item?.name}
            </span>
            <Popover
              content={content}
              title="Product Details"
              className="w-[45px]"
            >
              <IoInformationCircle className="text-xl text-blue-500" />
            </Popover>
          </div>
        );
      },
    },
    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
      render: (_, record) => (
        <span>{record?.itemId?.uom || record?.item?.uom}</span>
      ),
    },
    {
      title: 'Units',
      dataIndex: 'units',
      key: 'units',
      render: (_, record) => <span>{record?.units}</span>,
    },
    {
      title: 'Template',
      dataIndex: 'template',
      key: 'template',
      render: (_, record) => <span>{record?.jobTemplate?.name}</span>,
    },
    {
      title: 'Input Screen',
      dataIndex: 'inputScreen',
      key: 'inputScreen',
      render: (_, record) => (
        <span>{record?.jobTemplate?.inputScreen?.name}</span>
      ),
    },
    {
      title: 'Production Flow',
      dataIndex: 'productionFlow',
      key: 'productionFlow',
      render: (_, record) => <span>{record?.jobTemplate?.flow?.name}</span>,
    },
    {
      title: '',
      dataIndex: 'raiseJobRequests',
      key: 'raiseJobRequests',
      render: (_, record, idx) => (
        <Button
          type="primary"
          onClick={() => {
            if (!record?.isBom) {
              setAddJobDetails((prev) => ({
                ...prev,
                bomItemId: record?._id,
                isOpen: true,
                id: idx + 1,
                parentItemId: record?.parentItemId,
                isForBom: false,
                template: record?.jobTemplate?._id,
                units: record?.units,
                inStock: record?.itemId?.quantity,
                jobTemplate: record?.jobTemplate,
                assemblyItemToUpdate: record?._id,
                assemblyPart:
                  record?.itemType === 'Part' && record?.itemId?._id,
                assemblyProduct:
                  record?.itemType === 'Product' && record?.itemId?._id,
              }));
            } else {
              setAddJobDetails((prev) => ({
                ...prev,
                bomItemId: record?.indexId,
                isOpen: true,
                id: idx + 1,
                parentItemId: record?.parentItemId,
                isForBom: true,
                template: record?.jobTemplate?._id,
                units: record?.multiplier * record?.parentUnits,
                inStock: record?.item?.quantity,
                jobTemplate: record?.jobTemplate,
                assemblyItemToUpdate: record?.indexId,
                assemblyPart: record?.itemType === 'Part' && record?.item?._id,
                assemblyProduct:
                  record?.itemType === 'Product' && record?.item?._id,
              }));
            }
          }}
        >
          Raise Job Request
        </Button>
      ),
    },
  ];

  const jobCreatedColumns = [
    {
      title: 'Item',
      dataIndex: 'item',
      key: 'item',
      render: (_, record) => (
        <span>{record?.itemId?.name || record?.item?.name}</span>
      ),
    },
    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
      render: (_, record) => (
        <span>{record?.itemId?.uom || record?.item?.uom}</span>
      ),
    },
    {
      title: 'Units',
      dataIndex: 'units',
      key: 'units',
      render: (_, record) => <span>{record?.units}</span>,
    },
    // {
    //   title: 'Input Screen',
    //   dataIndex: 'inputScreen',
    //   key: 'inputScreen',
    //   render: (_, record) => (
    //     <span>{record?.jobTemplate?.inputScreen?.name}</span>
    //   ),
    // },
    {
      title: 'Production Flow',
      dataIndex: 'productionFlow',
      key: 'productionFlow',
      render: (_, record) => <span>{record?.jobTemplate?.flow?.name}</span>,
    },
    {
      title: 'Model Name',
      dataIndex: 'modelName',
      key: 'modelName',
      render: (_, record) => (
        <span>
          {
            record?.createInput?.find(
              (el) =>
                el?.isCreatedFromWorkOrderSideBar &&
                (el?.bomItemId?.toString() === record?._id?.toString() ||
                  el?.bomItemId?.toString() === record?.indexId?.toString())
            )?.modelName
          }
        </span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => (
        <span
          className={`py-[4px] px-4 rounded text-white text-[10px] ${record?.status === 'notStarted' ? 'bg-red-500' : record?.status === 'ongoing' ? 'bg-yellow-500' : 'bg-green-500'}`}
        >
          {record?.createInput
            ?.find(
              (el) =>
                el?.isCreatedFromWorkOrderSideBar &&
                (el?.bomItemId?.toString() === record?._id?.toString() ||
                  el?.bomItemId?.toString() === record?.indexId?.toString())
            )
            ?.status?.toUpperCase()}
        </span>
      ),
    },
  ];

  const outsourceJobsColumnRequests = [
    {
      title: 'Item',
      dataIndex: 'item',
      key: 'item',
      render: (_, record) => (
        <span>{record?.manualEntry || record?.itemId?.name || '-'}</span>
      ),
    },
    {
      title: 'Units',
      dataIndex: 'units',
      key: 'units',
      render: (_, record) => <span>{record?.units || '-'}</span>,
    },
    {
      title: 'Valuation',
      dataIndex: 'valuation',
      key: 'valuation',
      render: (_, record) => <span>{record?.itemId?.valuation || '-'}</span>,
    },
  ];

  const outsourceJobsCreated = [
    {
      title: 'Item',
      dataIndex: 'item',
      key: 'item',
      render: (_, record) => (
        <span>{record?.outsourceJobs?.itemName || '-'}</span>
      ),
    },
    {
      title: 'Required Stock',
      dataIndex: 'requiredStock',
      key: 'requiredStock',
      render: (_, record) => (
        <span>{record?.outsourceJobs?.requiredStock || '-'}</span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => (
        <span
          className={`py-[4px] px-4 rounded text-white text-[10px] ${record?.outsourceJobs?.status === 'pending' ? 'bg-yellow-500' : 'bg-green-500'}`}
        >
          {record?.outsourceJobs?.status?.toUpperCase()}
        </span>
      ),
    },
  ];

  const raiseIndentsFunction = async () => {
    let data = {};
    if (selectedIndents?.length > 0) {
      data = {
        indents: selectedIndents,
      };
    } else {
      data = {
        indents: workOrder?.indentsToBeCreated,
      };
    }

    const res = await raiseIndents({ id: selectedWorkOrder, data });
    if (res?.error === undefined) {
      setSelectedIndents([]);
      toast.success('INDENTS RAISED');
    }
  };

  const raiseStockRequestsFunction = async () => {
    let data = {};
    if (selectedStoreRequests?.length > 0) {
      data = {
        requests: selectedStoreRequests,
      };
    } else {
      data = {
        requests: workOrder?.storeRequestToBeRaised,
      };
    }

    const res = await raiseStoreRequests({ id: selectedWorkOrder, data });
    if (res?.error === undefined) {
      setSelectedStoreRequests([]);
      toast.success('STORE REQUESTS RAISED');
    }
  };

  const raiseJobRequestsFunction = async (jobData) => {
    let temp = [jobData];

    temp[0].assemblyItemToUpdate = addJobDetails?.bomItemId;
    let data = {
      allJobData: temp,
    };

    const res = await raiseJobRequests({ id: selectedWorkOrder, data });
    if (res?.error === undefined) {
      // setAllJobData([]);
      setAddJobDetails({
        isOpen: false,
        id: null,
        template: null,
      });
      // setJobSubmitStatus(false);
      toast.success('JOB REQUESTS RAISED');
    }
  };

  const createOutsourceJobsFunction = async () => {
    let data = {};
    if (selectedOutsourceJobRequests?.length > 0) {
      data = {
        items: selectedOutsourceJobRequests,
      };
    } else {
      data = {
        items: workOrder?.items?.filter(
          (elem) => elem?.category === 'outsource'
        ),
      };
    }

    const res = await createOutsourceJobs({ id: selectedWorkOrder, data });
    if (res?.error === undefined) {
      setSelectedOutsourceJobRequests([]);
      toast.success('OUTSOURCE JOBS CREATED');
    }
  };

  const onIndentSelectChange = (_, selectedRows) => {
    setSelectedIndents(selectedRows);
  };
  const indentRowSelection = {
    selectedRowKeys: selectedIndents?.map((elem) => elem?.id),
    onChange: onIndentSelectChange,
  };

  const onStockRequestSelectChange = (_, selectedRows) => {
    setSelectedStoreRequests(selectedRows);
  };
  const storeRequestRowSelection = {
    selectedRowKeys: selectedStoreRequests?.map((elem) => elem?.id),
    onChange: onStockRequestSelectChange,
  };

  const onOutsourceJobRequestSelectChange = (_, selectedRows) => {
    setSelectedOutsourceJobRequests(selectedRows);
  };
  const outsourceJobRequestRowSelection = {
    selectedRowKeys: selectedOutsourceJobRequests?.map((elem) => elem?._id),
    onChange: onOutsourceJobRequestSelectChange,
  };

  const RenderIndentRequests = () => {
    return (
      <>
        {workOrder?.isMigrated ? (
          <>
            <div className="flex items-center justify-center bg-slate-100 py-6 rounded">
              <h4 className="text-slate-300">N/A</h4>
            </div>
          </>
        ) : (
          <>
            {workOrder?.indentsToBeCreated?.products?.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="my-2">Indent Requests</h4>
                  <Button type="primary" onClick={raiseIndentsFunction}>
                    {selectedIndents?.length === 0
                      ? 'Raise All Indents'
                      : 'Raise Selected Indents'}
                  </Button>
                </div>
                <Table
                  rowSelection={{
                    type: 'checkbox',
                    ...indentRowSelection,
                  }}
                  rowKey={(record) => record?.id}
                  columns={indentAndStoreColumns}
                  dataSource={workOrder?.indentsToBeCreated?.products}
                  pagination={false}
                  scroll={{ x: 'max-content' }}
                  className="rounded-lg"
                />
              </div>
            )}
            <div className="mt-4 mb-2">
              <h4>Indents Raised</h4>
              <Table
                rowKey={(record) => record?._id}
                columns={indentsRaisedColumns}
                dataSource={workOrder?.indents}
                pagination={false}
                scroll={{ x: 'max-content' }}
                className="rounded-lg"
              />
            </div>
          </>
        )}
      </>
    );
  };

  const RenderStoreRequests = () => {
    return (
      <>
        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="my-2">Store Requests</h4>
            {workOrder?.storeRequestToBeRaised?.length > 0 && (
              <Button type="primary" onClick={raiseStockRequestsFunction}>
                {selectedStoreRequests?.length === 0
                  ? 'Raise All Requests'
                  : 'Raise Selected Requests'}
              </Button>
            )}
          </div>
          <Table
            rowSelection={{
              type: 'checkbox',
              ...storeRequestRowSelection,
            }}
            rowKey={(record) => record?.id}
            columns={indentAndStoreColumns}
            dataSource={workOrder?.storeRequestToBeRaised}
            pagination={false}
            scroll={{ x: 'max-content' }}
            className="rounded-lg"
          />
          <div className="mt-4 mb-2">
            <h4>Store Requests Raised</h4>
            <Table
              rowKey={(record) => record?._id}
              columns={storeRequestRaisedColumns}
              dataSource={workOrder?.storeRequests}
              pagination={false}
              scroll={{ x: 'max-content' }}
              className="rounded-lg"
            />
          </div>
        </div>
      </>
    );
  };

  const RenderJobRequests = () => {
    const jobReqests = [];
    const createdJobs = [];

    workOrder?.items?.forEach((elem) => {
      if (elem?.jobTemplate && !elem?.jobCreated) {
        jobReqests.push({ ...elem, isBom: false, parentItemId: elem?._id });
      }
      if (elem?.jobCreated && elem?.jobTemplate) createdJobs.push(elem);
      if (elem?.bom?.item?.length > 0) {
        elem?.bom?.item?.forEach((item) => {
          if (item?.jobTemplate && !item?.jobCreated) {
            jobReqests.push({
              ...item,
              isBom: true,
              parentUnits: elem?.units,
              parentItemId: elem?._id,
            });
          }
          if (item?.jobCreated && item?.jobTemplate)
            createdJobs.push({ ...item, createInput: elem?.createInput });
        });
      }
    });

    return (
      <>
        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="my-2">Job Requests</h4>
          </div>
          <Table
            rowKey={(record) => record?._id}
            columns={jobRequestColumns}
            // dataSource={workOrder?.items?.filter(
            //   (elem) => !elem?.jobCreated && elem?.jobTemplate !== undefined
            // )}
            dataSource={jobReqests}
            pagination={false}
            scroll={{ x: 'max-content' }}
            className="rounded-lg"
          />
        </div>
        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="my-2">Jobs Created</h4>
          </div>
          <Table
            rowKey={(record) => record?._id}
            columns={jobCreatedColumns}
            // dataSource={workOrder?.items?.filter((elem) => elem?.jobCreated)}
            dataSource={createdJobs}
            pagination={false}
            scroll={{ x: 'max-content' }}
            className="rounded-lg"
          />
        </div>
      </>
    );
  };

  const RenderOutSourceJobRequests = () => {
    return (
      <>
        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="my-2">Outsource Job Requests</h4>
            {workOrder?.items?.filter(
              (elem) =>
                !elem?.outsourceJobsCreated && elem?.category === 'outsource'
            )?.length !== 0 && (
              <Button type="primary" onClick={createOutsourceJobsFunction}>
                {selectedOutsourceJobRequests?.length === 0
                  ? 'Raise All Requests'
                  : 'Raise Selected Requests'}
              </Button>
            )}
          </div>
          <Table
            rowSelection={{
              type: 'checkbox',
              ...outsourceJobRequestRowSelection,
            }}
            rowKey={(record) => record?._id}
            columns={outsourceJobsColumnRequests}
            dataSource={workOrder?.items?.filter(
              (elem) =>
                elem?.category === 'outsource' &&
                elem?.outsourceJobsCreated === false
            )}
            pagination={false}
            scroll={{ x: 'max-content' }}
            className="rounded-lg"
          />
        </div>
        {workOrder?.items?.filter?.((elem) => elem?.outsourceJobsCreated)
          ?.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-2">
              <h4 className="my-2">Outsource Jobs Created</h4>
            </div>
            <Table
              rowKey={(record) => record?._id}
              columns={outsourceJobsCreated}
              dataSource={workOrder?.items?.filter(
                (elem) =>
                  elem?.category === 'outsource' &&
                  elem?.outsourceJobsCreated === true
              )}
              pagination={false}
              scroll={{ x: 'max-content' }}
              className="rounded-lg"
            />
          </div>
        )}
      </>
    );
  };

  const RenderGeneralDetails = () => {
    return (
      <>
        <div>
          <h4 className="my-2">General Details</h4>
          <div className="border rounded p-4">
            <GeneralDetailsRow
              name={'Work Order Id'}
              Icon={IdcardOutlined}
              value={workOrder?.workOrderId}
              color={'text-indigo-500'}
            />
            <GeneralDetailsRow
              name={'Name'}
              Icon={IdcardOutlined}
              value={workOrder?.name}
              color={'text-purple-500'}
              className="mt-2"
            />
            <GeneralDetailsRow
              name={'Created By'}
              Icon={UserOutlined}
              value={workOrder?.createdBy}
              color={'text-green-500'}
              className="mt-2"
            />
            <GeneralDetailsRow
              name={'Order Quantity'}
              Icon={ShoppingCartOutlined}
              value={workOrder?.orderQuantity}
              color={'text-yellow-500'}
              className="mt-2"
            />
          </div>
        </div>
        {workOrder?.additionalFields?.templateData?.length > 0 && (
          <div>
            <h4 className="mt-2">Template Details</h4>
            <ShowTemplateValues
              template={workOrder?.additionalFields?.templateData}
              showTitle={false}
            />
          </div>
        )}
        <div>
          <h4 className="my-2">Item Details</h4>
          <Table
            columns={itemColumns}
            dataSource={workOrder?.items}
            rowKey="_id"
            pagination={false}
            scroll={{ x: 'max-content' }}
            className="rounded-lg"
          />
        </div>
      </>
    );
  };

  const tabItems = [
    {
      label: `Work Order`,
      key: 'workOrder',
    },
    {
      label: `Indents`,
      key: 'indents',
    },
    {
      label: `Store Requests`,
      key: 'storeRequests',
    },
    {
      label: `Job Requests`,
      key: 'jobRequests',
    },
    {
      label: `Outsource Job Requests`,
      key: 'outsourceJobRequests',
    },
  ];

  // useEffect(() => {
  //   if (jobSubmitStatus) {
  //     // (async () => {
  //     //   await raiseJobRequestsFunction();
  //     // })();
  //   }
  // }, [jobSubmitStatus]); //eslint-disable-line

  return (
    <>
      <ItemModal
        items={bomToShow?.bom}
        showModal={bomToShow?.open}
        orderQuantity={bomToShow?.orderQuantity}
        closeModal={closeBomModal}
        allParts={allParts}
        allProducts={allProducts}
        type="bom"
      />
      <ItemModal
        items={rmToShow?.rawMaterials}
        assets={rmToShow?.assets}
        showModal={rmToShow?.open}
        orderQuantity={rmToShow?.orderQuantity}
        closeModal={closeRMModal}
        allParts={allParts}
        allProducts={allProducts}
        type="rm"
      />
      {addJobDetails?.isOpen && (
        <AddJobDetailsModal
          workOrderId={workOrder?.workOrderId}
          setAddJobDetails={setAddJobDetails}
          // allJobData={allJobData}
          // setAllJobData={setAllJobData}
          addJobDetails={addJobDetails}
          orderQuantity={workOrder?.orderQuantity}
          // deadline={DeadlineDate}
          tempDefaults={tempDefaults}
          setTempDefaults={setTempDefaults}
          submitHandler={raiseJobRequestsFunction}
          // submitHandler={async (jobData) => {
          //   await raiseJobRequestsFunction(jobData);
          //   setJobSubmitStatus(true);
          // }}
        />
      )}
      {fromKanban ? (
        <div>
          <Tabs
            defaultActiveKey="workOrder"
            centered
            items={tabItems}
            onChange={(e) => {
              setSelectedTab(e);
            }}
          />
          {selectedTab === 'workOrder' && RenderGeneralDetails()}
          {selectedTab === 'indents' && RenderIndentRequests()}
          {selectedTab === 'storeRequests' && RenderStoreRequests()}
          {selectedTab === 'jobRequests' && RenderJobRequests()}
          {selectedTab === 'outsourceJobRequests' &&
            RenderOutSourceJobRequests()}
        </div>
      ) : (
        <RightSidebar
          scale={800}
          openSideBar={openSideBar}
          setOpenSideBar={setOpenWorkOrderSidebar}
          title="Work Order Information"
          onClose={onCloseSidebar}
        >
          {isWorkOrderLoading ? (
            <Spinner />
          ) : (
            <>
              <Tabs
                defaultActiveKey="workOrder"
                centered
                items={tabItems}
                onChange={(e) => {
                  setSelectedTab(e);
                }}
              />
              {selectedTab === 'workOrder' && RenderGeneralDetails()}
              {selectedTab === 'indents' && RenderIndentRequests()}
              {selectedTab === 'storeRequests' && RenderStoreRequests()}
              {selectedTab === 'jobRequests' && RenderJobRequests()}
              {selectedTab === 'outsourceJobRequests' &&
                RenderOutSourceJobRequests()}
            </>
          )}
        </RightSidebar>
      )}
    </>
  );
};

export default WorkOrderSidebarV2;
