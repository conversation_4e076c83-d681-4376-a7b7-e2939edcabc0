const pagesToEditAfterApproval = [
  'quotation',
  'salesOrder',
  'requestForQuotation',
  'purchaseOrder',
];

const EditAfterApproval = ({ defaults, setDefaults }) => {
  return (
    <div className="w-[100%] border-b-2 text-sm  my-3 border-gray-400/70">
      <div className="grid grid-cols-1 md:grid-cols-2 w-full py-2 justify-center items-center">
        <h3 className="text-gray-subHeading">Allow Edit After Approval :</h3>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={pagesToEditAfterApproval?.every((v) =>
              defaults?.projectDefaults?.allowEditAfterApproval?.includes(v)
            )}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  allowEditAfterApproval: e.target.checked
                    ? [
                        'quotation',
                        'salesOrder',
                        'requestForQuotation',
                        'purchaseOrder',
                      ]
                    : [],
                },
              }));
            }}
          />
          <p>Allow Edit After Approval for All</p>
        </div>
      </div>
      <div className="grid grid-cols-2 w-full py-2 justify-center items-center gap-y-2">
        {pagesToEditAfterApproval.map((page) => (
          <div key={page} className="flex items-center gap-2">
            <input
              type="checkbox"
              className="w-4 h-4"
              checked={defaults?.projectDefaults?.allowEditAfterApproval?.includes(
                page
              )}
              onChange={(e) => {
                setDefaults((prev) => ({
                  ...prev,
                  projectDefaults: {
                    ...prev.projectDefaults,
                    allowEditAfterApproval: e.target.checked
                      ? [...prev.projectDefaults.allowEditAfterApproval, page]
                      : prev.projectDefaults.allowEditAfterApproval.filter(
                          (v) => v !== page
                        ) || [],
                  },
                }));
              }}
            />
            <p>{`Allow Edit After Approval for ${page.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase())}`}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EditAfterApproval;
