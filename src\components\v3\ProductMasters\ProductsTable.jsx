import { LoadingOutlined } from '@ant-design/icons';
import { Table as AntTable, Tooltip as AntTooltip, Button, Checkbox, Image, Spin } from 'antd';
import { ArrowDown, ArrowUp, Edit, Trash2 } from 'lucide-react';
import { useCallback, useContext, useRef, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';
import ImportIcon from '../../../assets/images/inward.png';
import noPartImage from '../../../assets/images/no_partImage.jpg';
import {
  getLocalDateTime,
  mobileWidth,
} from '../../../helperFunction';
import useDebounceValue from '../../../hooks/useDebounceValue';
import {
  useAddManyProductsWithVariantsMutation,
  useDeleteManyProductsMutation,
  useGetProductFilterOptionsQuery,
  usePaginateProductsQuery,
} from '../../../slices/productApiSlice';
import { Store } from '../../../store/Store';
import { PAGINATION_LIMIT } from '../../../utils/Constant';
import TruncateString from '../../global/TruncateString';
import { FilterIcon, FilterV2 } from '../../global/components/FilterV2';
import Pagination from '../../global/components/Pagination';
import { convertToJson } from '../../importmaster/utils';
import ProductSideBar from './ProductSideBar';


const ProductsTable = () => {
  const [checkedRows, setCheckedRows] = useState([]);
  const navigate = useNavigate();

  const importRef = useRef();

  const isMobile = useMediaQuery({ query: mobileWidth });
  const [sideBarData, setSideBarData] = useState({});
  const [field, setField] = useState('createdAt');
  const [type, setType] = useState('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState([]);
  const { data: filterOptions } = useGetProductFilterOptionsQuery();

  const filterConfig = [
    {
      label: 'Date',
      path: 'createdAt',
      type: 'date',
      key: 'createdAt',
    },
    {
      label: 'Id',
      path: 'id',
      type: 'multiSelect',
      key: 'id',
      options: filterOptions?.id || [],
    },
    {
      label: 'Name',
      path: 'name',
      type: 'multiSelect',
      key: 'name',
      options: filterOptions?.name || [],
    },
    {
      label: 'UOM',
      path: 'uom',
      type: 'multiSelect',
      key: 'uom',
      options: filterOptions?.uom || [],
    },
  ];

  const [deleteProducts] = useDeleteManyProductsMutation();
  const [searchTerm, setSearchTerm] = useState('');
  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [page, setPage] = useState(1);
  const [ShowSidebar, setShowSidebar] = useState(false);
  const [selectAll, setSelectAll] = useState(false);

  const [addManyProductsWithVariants, { isLoading: isLoadingAddMany }] =
    useAddManyProductsWithVariantsMutation();

  const debounceSearch = useDebounceValue(searchTerm) || '';

  const { data: pageData } = usePaginateProductsQuery(
    {
      page,
      limit,
      debounceSearch,
      filters,
      type,
      field,
    },
    { refetchOnMountOrArgChange: true, skip: !page || !limit }
  );

  const {
    defaults: { defaultParam },
  } = useContext(Store);

  // Handle row selection
  const handleRowSelect = useCallback((record, checked) => {
    if (checked) {
      setCheckedRows(prev => [...prev, record]);
    } else {
      setCheckedRows(prev => prev.filter(item => item._id !== record._id));
      setSelectAll(false);
    }
  }, []);

  // Handle select all
  const handleSelectAll = useCallback((e) => {
    const checked = e.target.checked;
    setSelectAll(checked);
    if (checked) {
      setCheckedRows(pageData?.results || []);
    } else {
      setCheckedRows([]);
    }
  }, [pageData?.results]);

  const handleEdit = useCallback((data) => {
    navigate(`manage/${data?._id}`);
  }, [navigate]);

  const columns = [
    {
      title: (
        <Checkbox
          checked={selectAll}
          indeterminate={checkedRows.length > 0 && checkedRows.length < (pageData?.results?.length || 0)}
          onChange={handleSelectAll}
        >
          Select All
        </Checkbox>
      ),
      dataIndex: 'select',
      key: 'select',
      width: 80,
      render: (_, record) => (
        <Checkbox
          className='font-semibold'
          checked={checkedRows.some(item => item._id === record._id)}
          onChange={(e) => handleRowSelect(record, e.target.checked)}
        />
      ),
    },
    {
      title: <div className="flex items-center gap-2">
        Date
        {type === 'aesc' && field === 'createdAt' ? (
          <ArrowUp
            cursor={'pointer'}
            size={15}
            onClick={() => {
              setField('createdAt');
              setType('desc');
            }}
          />
        ) : (
          <ArrowDown
            cursor={'pointer'}
            size={15}
            onClick={() => {
              setField('createdAt');
              setType('aesc');
            }}
          />
        )}
      </div>,
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date) => getLocalDateTime(date),
    },
    {
      title: 'Thumbnail',
      dataIndex: 'thumbNail',
      key: 'thumbNail',
      width: 80,
      render: (thumbNail) => (
        <Image
          className="object-contain rounded-lg cursor-pointer bg-contain"
          src={thumbNail?.data || noPartImage}
          alt="Product thumbnail"
          width={48}
          height={48}
          fallback={noPartImage}
        />
      ),
    },
    {
      title: defaultParam?.inventoryIdDefaults?.productDefaults === 'Id' ? 'FG Product ID' : 'Product ID',
      dataIndex: 'id',
      key: 'id',
      width: 150,
      render: (id) => (
        <TruncateString length={13}>
          {id || '-'}
        </TruncateString>
      ),
    },
    {
      title: <div className="flex items-center gap-2">
        Name
        {type === 'aesc' && field === 'name' ? (
          <ArrowUp
            cursor={'pointer'}
            size={15}
            onClick={() => {
              setField('name');
              setType('desc');
            }}
          />
        ) : (
          <ArrowDown
            cursor={'pointer'}
            size={15}
            onClick={() => {
              setField('name');
              setType('aesc');
            }}
          />
        )}
      </div>,
      dataIndex: 'name',
      key: 'name',
      width: 300,
      render: (name) => (
        <AntTooltip title={name}>
          <span className="cursor-pointer">
            {name?.length > 55 ? `${name.slice(0, 55)}...` : name || '-'}
          </span>
        </AntTooltip>
      ),
    },
    {
      title: 'Attachments',
      dataIndex: 'attachments',
      key: 'attachments',
      width: 120,
      render: (attachments) => (
        <span className="text-blue-600 text-xs cursor-pointer">
          Files <span className="text-[8px]">({attachments?.length || 0})</span>
        </span>
      ),
    },
    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
      width: 100,
      render: (uom) => uom || '-',
    },
    {
      title: 'Valuation Field',
      dataIndex: 'valuation',
      key: 'valuation',
      width: 150,
      render: (valuation) => valuation || '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      fixed: 'right',
      render: (_, record) => (
        <div className="flex gap-2">
          <AntTooltip title="Edit">
            <Edit
              className="h-4 w-4 cursor-pointer text-blue-500 hover:text-blue-700"
              onClick={() => handleEdit(record)}
            />
          </AntTooltip>
        </div>
      ),
    },
  ]

  const handleDelete = async () => {
    const idsToDelete = checkedRows?.map((item) => item._id);
    const res = await deleteProducts({ ids: idsToDelete });
    if (res) {
      toast.success('Products deleted Successfully');
    }
    setCheckedRows([]);
    setSelectAll(false);
  };

  const handleSearch = (term) => {
    setSearchTerm(term);
  };



  const downloadTemplate = () => {
    const template = [
      {
        Product: '',
        Variant1: '',
        Variant2: '',
        Unit: '',
        Category: '',
        Rate: '',
        HSN: '',
        CGST: '',
        SGST: '',
        IGST: '',
        Discount: '',
        Store1: '',
        Store2: '',
      },
    ];

    const ws = XLSX.utils.json_to_sheet(template);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Template');
    XLSX.writeFile(wb, 'productMasters.xlsx');
  };

  return (
    <div>
      {ShowSidebar && (
        <ProductSideBar
          openSideBar={ShowSidebar}
          setOpenSideBar={setShowSidebar}
          data={sideBarData}
        />
      )}
      {isLoadingAddMany && (
        <div className="flex w-full gap-5 justify-center items-center">
          <Spin indicator={<LoadingOutlined />} size="large" /> Importing your
          data from {importRef?.current?.value?.split('\\')?.[2] || 'Excel'}
          , Please be patient this can take some time.
        </div>
      )}
      <div className="flex justify-between w-full bg-white rounded-tl-lg rounded-tr-lg mt-2">
        <div className="flex w-full items-center py-2 justify-between">
          <div className="flex justify-between items-center relative">
            <div className="absolute top-[-5px] left-[-340px]  md:left-[-326px]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-5 absolute top-3 left-[22rem] text-gray-400 cursor-pointer"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                />
              </svg>
            </div>
            <input
              className="w-[190px] md:!min-w-[100px] !pl-10 ml-1 md:ml-3 md:w-[400px] !rounded-3xl !px-5 !py-1.5 outline-none !text-xs md:!text-sm bg-[#F2F1FB] mt-1 md:mt-0"
              placeholder="Search Items from list"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>

          <div className="flex gap-3 mr-4">
            {!isMobile && (
              <Button
                className="mt-1.5"
                onClick={handleDelete}
                type='text'
                disabled={checkedRows?.length < 1}
                icon={<Trash2 className="h-5 w-5" />}
                danger
              />
            )}

            {!isMobile && (
              <div className="mt-3">
                <FilterIcon
                  showFilters={showFilters}
                  setShowFilters={setShowFilters}
                />
              </div>
            )}
            <input
              type="file"
              className="hidden"
              ref={importRef}
              onChange={(e) => {
                const file = e.target?.files?.[0];
                const readFile = () => {
                  const reader = new FileReader();

                  reader.onload = async (evt) => {
                    const bstr = evt.target.result;
                    const wb = XLSX.read(bstr, { type: 'binary' });
                    let payload = [];
                    for (let i = 0; i < wb.SheetNames.length; i++) {
                      const wsname = wb.SheetNames[i];
                      const ws = wb.Sheets[wsname];

                      const data = XLSX.utils.sheet_to_csv(ws, {
                        header: 1,
                      });
                      payload.push(...convertToJson(data));
                    }

                    addManyProductsWithVariants({
                      data: { productsList: payload },
                    })
                      .unwrap()
                      .then((res) => {
                        if (res?.status === 200) {
                          toast.success('Data imported successfully');
                        }
                        importRef.current.value = '';
                      });
                  };
                  reader.readAsBinaryString(file);
                };

                readFile();
              }}
            />

            <Button
              disabled={isLoadingAddMany}
              className={`!bg-green-600 !text-white border rounded-lg !h-7 !text-[11px] md:!h-[30px] md:!text-[13px] md:!px-[2px] !min-w-[4rem] mt-[1px] md:mt-[5px] !px-0 !w-[6rem]`}
              onClick={() => importRef.current.click()}
            >
              <img
                src={ImportIcon}
                alt="Import Icon"
                className="w-4 h-4 object-contain relative"
              />
              Import
            </Button>

            <Button
              className={`!bg-blue-500 !text-white border rounded-lg !h-7 !text-[11px] md:!h-[30px] md:!text-[13px] md:!px-[2px] !min-w-[4rem] mt-[1px] md:mt-[5px] !px-0 !w-[6rem]`}
              onClick={downloadTemplate}
            >
              Sample File
            </Button>

            <Button
              className={`border rounded-lg !h-7 !text-[11px] md:!h-[30px] md:!text-[13px] md:!px-[2px] !min-w-[4rem] mt-[1px] md:mt-[5px] !px-0 !w-[6rem]`}
              onClick={() => navigate('manage')}
            >
              + Add Row
            </Button>
          </div>
        </div>
      </div>
      {!pageData?.isFetching && (
        <>
          <FilterV2
            showFilters={showFilters}
            config={filterConfig}
            setFilters={setFilters}
          />
          <div className="bg-white rounded-lg shadow-sm">
            <AntTable
              columns={columns}
              dataSource={pageData?.results || []}
              rowKey="_id"
              pagination={false}
              loading={pageData?.isFetching}
              scroll={{ x: 'max-content' }}
              size="middle"
              className="rounded-lg"
              onRow={(record) => ({
                onClick: () => {
                  setSideBarData(record);
                  setShowSidebar(true);
                },
                className: 'cursor-pointer hover:bg-gray-50',
              })}
              expandable={{
                childrenColumnName: 'undefined',
              }}
            />

          </div>

          <Pagination
            limit={limit}
            page={page}
            totalPages={pageData?.totalPages}
            totalResults={pageData?.totalResults}
            setPage={setPage}
            setLimit={setLimit}
            className={`w-full`}
          />
        </>
      )
      }
    </div >
  );
};


export default ProductsTable;
