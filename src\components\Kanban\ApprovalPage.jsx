import { useContext, useEffect, useMemo, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import {
  generateDateString,
  mobileWidth,
  printWithSpecialFileName,
  tabletWidth,
  unCamelCaseString,
} from '../../helperFunction';
import PurchasePdf from '../../pdfGeneratorFiles/purchaseOrderPdf';
import { useGetAllPendingStatusDataQuery } from '../../slices/kanbanApiSlice';
import { Store } from '../../store/Store';
import QuotationPDF from '../SalesOrderManagement/Quotation/QuotationPDF';
import RightSidebar from '../global/components/RightSidebar';
import Select from '../global/components/Select';
import Spinner from '../global/components/Spinner';
import { TabButton, TabContainer } from '../global/components/TabContainer';
import SalesReport from '../salesOrder/salesReport';
import { Label } from '../v2';
import MediaModal from '../v3/global/components/MediaModal';
import ApprovalCard from './ApprovalCard';
import ApprovalSidebarData from './ApprovalSidebarData';
import SendApprovalMail from './SendApprovalMail';

function ApprovalPage() {
  const { data: pendingData, isFetching } = useGetAllPendingStatusDataQuery();
  const [selectedTab, setSelectedTab] = useState('');
  const [ShowSidebar, setShowSidebar] = useState(false);
  const [SidebarData, setSidebarData] = useState({});
  const [SidebarDataType, setSidebarDataType] = useState('');
  const [SelectedTabForPrint, setSelectedTabForPrint] = useState('');
  const [Total, setTotal] = useState(0);
  const [DataToPrint, setDataToPrint] = useState(null);
  const [ShowEmailModal, setShowEmailModal] = useState(false);
  const [DataForMail, setDataForMail] = useState(null);
  const [Media, setMedia] = useState([]);
  const [ReadMore, setReadMore] = useState(false);
  const [allCount, setAllCount] = useState(0);
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const disableMap = {
    purchaseIndents: 'indent',
    purchaseOrders: 'purchaseOrder',
    salesQuotations: 'quotation',
    salesInvoices: 'salesInvoice',
    salesOrders: 'salesOrder',
  };

  const disabledPages = (
    defaultParam?.projectDefaults?.disabledApprovalFor || []
  )?.map(
    (key) =>
      Object.keys(disableMap).find((mapKey) => disableMap[mapKey] === key) ||
      key
  );

  const options = useMemo(
    () =>
      Object.keys(pendingData || {})
        .filter(
          (key) =>
            key !== 'noAccess' &&
            !pendingData?.noAccess?.includes(key) &&
            !disabledPages.includes(key)
        )
        .map((key) => ({
          label: unCamelCaseString(key).replace(' ', '\xA0').trim(),
          value: key,
        })),
    [pendingData, disabledPages]
  );

  useEffect(() => {
    let timer = null;
    let sum = 0;
    if (SelectedTabForPrint && DataToPrint) {
      DataToPrint?.productDetails?.map((item) => {
        sum += item?.totalAmount || 0;
      });
      setTotal(sum);
      clearTimeout(timer);
      timer = setTimeout(() => {
        printWithSpecialFileName();
      }, 400);
    }
    return () => {
      clearTimeout(timer);
    };
  }, [SelectedTabForPrint, DataToPrint]);

  window.onafterprint = () => {
    setSelectedTabForPrint('');
    setDataToPrint(null);
  };

  const [reversedSelectedTabOrders, setReversedSelectedTabOrders] = useState(
    []
  );

  useEffect(() => {
    if (pendingData && selectedTab) {
      const reversedOrders = [...pendingData[selectedTab]].reverse();
      setReversedSelectedTabOrders(reversedOrders);
    }
    let val = 0;
    for (let key in pendingData) {
      if (key !== 'noAccess') val += pendingData?.[key]?.length || 0;
    }
    setAllCount(val);
  }, [pendingData, selectedTab]);

  if (isFetching) return <Spinner />;

  return (
    <>
      {ShowEmailModal && (
        <SendApprovalMail
          isMobile={isMobile}
          isTablet={isTablet}
          selectedtab={SelectedTabForPrint}
          setShowEmailModal={setShowEmailModal}
          data={DataForMail}
        />
      )}
      {SelectedTabForPrint === 'purchaseOrders' && DataToPrint && (
        <PurchasePdf data={DataToPrint} />
      )}
      {SelectedTabForPrint === 'salesQuotations' && DataToPrint && (
        <QuotationPDF
          data={DataToPrint}
          total={Total}
          generateDateString={generateDateString}
        />
      )}
      {SelectedTabForPrint === 'salesOrders' && DataToPrint && (
        <SalesReport data={DataToPrint} />
      )}
      <div>
        <RightSidebar
          openSideBar={ShowSidebar}
          setOpenSideBar={setShowSidebar}
          className={'w-full md:w-1/3'}
        >
          <ApprovalSidebarData
            data={SidebarData}
            tab={SidebarDataType}
            setReadMore={setReadMore}
            setShowSidebar={setShowSidebar}
            setMedia={setMedia}
            showSidebar={ShowSidebar}
          />
        </RightSidebar>
        {ReadMore && (
          <MediaModal
            ShowModal={ReadMore}
            FormData={Media}
            setShowModal={setReadMore}
            isView={true}
          />
        )}
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-3 md:mx-[-1rem] lg:mx-[6rem]">
          <div className="flex items-center justify-start gap-x-3">
            <TabContainer className="!m-0">
              <TabButton
                isactive={!selectedTab ? true : false}
                onClick={() => {
                  setSelectedTab('');
                }}
                className={'flex items-center gap-x-2'}
              >
                Pending Approvals
                <p className="flex items-center justify-center text-[13px] bg-blue-500 text-white rounded-full px-2">
                  {!selectedTab ? allCount : pendingData?.[selectedTab].length}
                </p>
              </TabButton>
            </TabContainer>
          </div>
          <div className="w-full md:w-[14rem] mt-4 md:mt-0 lg:mt-0">
            <Label>Select Approval</Label>
            <Select
              options={[{ label: 'All Options', value: '' }, ...options]}
              value={selectedTab}
              onChange={(e) => {
                setSelectedTab(e.target.value);
              }}
            />
          </div>
        </div>

        <div className="md:mx-[-14px] lg:mx-[6rem]">
          {!selectedTab ? (
            <>
              {pendingData &&
                !defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                  'indent'
                ) &&
                pendingData?.purchaseIndents?.map((item) => (
                  <ApprovalCard
                    key={item._id}
                    selectedTab={'purchaseIndents'}
                    data={item}
                    setShowSidebar={setShowSidebar}
                    setSidebarData={setSidebarData}
                    setSidebarDataType={setSidebarDataType}
                    setSelectedTabForPrint={setSelectedTabForPrint}
                    setDataToPrint={setDataToPrint}
                    setShowEmailModal={setShowEmailModal}
                    setDataForMail={setDataForMail}
                  />
                ))}

              {pendingData &&
                !defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                  'purchaseOrder'
                ) &&
                pendingData?.purchaseOrders.map((item) => (
                  <ApprovalCard
                    key={item._id}
                    selectedTab={'purchaseOrders'}
                    data={item}
                    setShowSidebar={setShowSidebar}
                    setSidebarData={setSidebarData}
                    setSidebarDataType={setSidebarDataType}
                    setSelectedTabForPrint={setSelectedTabForPrint}
                    setDataToPrint={setDataToPrint}
                    setShowEmailModal={setShowEmailModal}
                    setDataForMail={setDataForMail}
                  />
                ))}

              {pendingData &&
                !defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                  'salesOrder'
                ) &&
                pendingData?.salesOrders?.map((item) => (
                  <ApprovalCard
                    key={item._id}
                    selectedTab={'salesOrders'}
                    data={item}
                    setShowSidebar={setShowSidebar}
                    setSidebarData={setSidebarData}
                    setSidebarDataType={setSidebarDataType}
                    setSelectedTabForPrint={setSelectedTabForPrint}
                    setDataToPrint={setDataToPrint}
                    setShowEmailModal={setShowEmailModal}
                    setDataForMail={setDataForMail}
                  />
                ))}

              {pendingData &&
                !defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                  'quotation'
                ) &&
                pendingData?.salesQuotations?.map((item) => (
                  <ApprovalCard
                    key={item._id}
                    selectedTab={'salesQuotations'}
                    data={item}
                    setShowSidebar={setShowSidebar}
                    setSidebarData={setSidebarData}
                    setSidebarDataType={setSidebarDataType}
                    setSelectedTabForPrint={setSelectedTabForPrint}
                    setDataToPrint={setDataToPrint}
                    setShowEmailModal={setShowEmailModal}
                    setDataForMail={setDataForMail}
                  />
                ))}

              {pendingData &&
                Object.keys(pendingData || {})?.map((key) => {
                  if (
                    [
                      'purchaseOrders',
                      'purchaseIndents',
                      'salesQuotations',
                      'salesOrders',
                      'noAccess',
                    ].includes(key)
                  )
                    return null;

                  return (
                    <>
                      {pendingData?.[key].map((item) => (
                        <ApprovalCard
                          key={item._id}
                          selectedTab={key}
                          data={item}
                          setShowSidebar={setShowSidebar}
                          setSidebarData={setSidebarData}
                          setSidebarDataType={setSidebarDataType}
                          setSelectedTabForPrint={setSelectedTabForPrint}
                          setDataToPrint={setDataToPrint}
                          setShowEmailModal={setShowEmailModal}
                          setDataForMail={setDataForMail}
                        />
                      ))}
                    </>
                  );
                })}
            </>
          ) : (
            <>
              {pendingData?.[selectedTab]?.length === 0 ? (
                <p className="text-center">
                  No pending {unCamelCaseString(selectedTab)}
                </p>
              ) : (
                <>
                  {reversedSelectedTabOrders.map((item) => (
                    <ApprovalCard
                      key={item._id}
                      selectedTab={selectedTab}
                      data={item}
                      setShowSidebar={setShowSidebar}
                      setSidebarData={setSidebarData}
                      setSidebarDataType={setSidebarDataType}
                      setSelectedTabForPrint={setSelectedTabForPrint}
                      setDataToPrint={setDataToPrint}
                      setShowEmailModal={setShowEmailModal}
                      setDataForMail={setDataForMail}
                    />
                  ))}
                </>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
}

export default ApprovalPage;
