import { v4 as uuidv4 } from 'uuid';

export const defaultFields = [
  {
    fieldId: uuidv4(),
    fieldName: 'PRODUCT NAME',
    fieldType: 'text',
    isDefault: true,
  },
  {
    fieldId: uuidv4(),
    fieldName: 'UOM',
    fieldType: 'text',
    isDefault: true,
  },
  {
    fieldId: uuidv4(),
    fieldName: 'QUANTITY',
    fieldType: 'number',
    isDefault: true,
  },
  {
    fieldId: uuidv4(),
    fieldName: 'RATE',
    fieldType: 'number',
    isDefault: true,
    formatting: {
      isItPrice: true,
    },
  },
  {
    fieldId: uuidv4(),
    fieldName: 'DISCOUNT',
    fieldType: 'number',
    isDefault: true,
    formatting: {
      isItPercentage: true,
    },
  },
  {
    fieldId: uuidv4(),
    fieldName: 'AMOUNT',
    fieldType: 'formula',
    isDefault: true,
    formatting: {
      isItPrice: true,
    },
    formula: '(QUANTITY * RATE) - (QUANTITY * RATE) * DISCOUNT / 100',
    formulaData: {
      parts: [
        {
          type: 'grouping',
          value: '(',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'QUANTITY',
          id: uuidv4(),
        },
        {
          type: 'operator',
          value: '*',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'RATE',
          id: uuidv4(),
        },
        {
          type: 'grouping',
          value: ')',
          id: uuidv4(),
        },
        {
          type: 'operator',
          value: '-',
          id: uuidv4(),
        },
        {
          type: 'grouping',
          value: '(',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'QUANTITY',
          id: uuidv4(),
        },
        {
          type: 'operator',
          value: '*',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'RATE',
          id: uuidv4(),
        },
        {
          type: 'grouping',
          value: ')',
          id: uuidv4(),
        },
        {
          type: 'operator',
          value: '*',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'DISCOUNT',
          id: uuidv4(),
        },
        {
          type: 'operator',
          value: '/',
          id: uuidv4(),
        },
        {
          type: 'number',
          value: '100',
          id: uuidv4(),
        },
      ],
    },
  },
  {
    fieldId: uuidv4(),
    fieldName: 'HSN/SAC CODE',
    fieldType: 'text',
    isDefault: true,
  },
  {
    fieldId: uuidv4(),
    fieldName: 'CGST RATE',
    fieldType: 'taxDropdown',
    isDefault: true,
    dropdownOptions: ['0%', '5%', '6%', '9%', '12%', '18%', '28%'],
    formatting: {
      isItPercentage: true,
    },
  },
  {
    fieldId: uuidv4(),
    fieldName: 'CGST AMOUNT',
    fieldType: 'formula',
    isDefault: true,
    formatting: {
      isItPrice: true,
    },
    formula: 'AMOUNT * CGST RATE',
    formulaData: {
      parts: [
        {
          type: 'field',
          value: 'AMOUNT',
          id: uuidv4(),
        },
        {
          type: 'operator',
          value: '%',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'CGST RATE',
          id: uuidv4(),
        },
      ],
    },
  },
  {
    fieldId: uuidv4(),
    fieldName: 'SGST RATE',
    fieldType: 'taxDropdown',
    isDefault: true,
    dropdownOptions: ['0%', '5%', '6%', '9%', '12%', '18%', '28%'],
    formatting: {
      isItPercentage: true,
    },
  },
  {
    fieldId: uuidv4(),
    fieldName: 'SGST AMOUNT',
    fieldType: 'formula',
    isDefault: true,
    formatting: {
      isItPrice: true,
    },
    formula: 'AMOUNT * SGST RATE',
    formulaData: {
      parts: [
        {
          type: 'field',
          value: 'AMOUNT',
          id: uuidv4(),
        },
        {
          type: 'operator',
          value: '%',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'CGST RATE',
          id: uuidv4(),
        },
      ],
    },
  },
  {
    fieldId: uuidv4(),
    fieldName: 'IGST RATE',
    fieldType: 'taxDropdown',
    isDefault: true,
    dropdownOptions: ['0%', '5%', '6%', '9%', '12%', '18%', '28%'],
    formatting: {
      isItPercentage: true,
    },
  },
  {
    fieldId: uuidv4(),
    fieldName: 'IGST AMOUNT',
    fieldType: 'formula',
    isDefault: true,
    formatting: {
      isItPrice: true,
    },
    formula: 'AMOUNT * IGST RATE',
    formulaData: {
      parts: [
        {
          type: 'field',
          value: 'AMOUNT',
          id: uuidv4(),
        },
        {
          type: 'operator',
          value: '%',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'CGST RATE',
          id: uuidv4(),
        },
      ],
    },
  },
  {
    fieldId: uuidv4(),
    fieldName: 'TOTAL AMOUNT',
    fieldType: 'formula',
    isDefault: true,
    formatting: {
      isItPrice: true,
    },
    formula: 'SUM(AMOUNT,CGST AMOUNT,SGST AMOUNT,IGST AMOUNT)',
    formulaData: {
      parts: [
        {
          type: 'function',
          value: 'SUM',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'AMOUNT',
          id: uuidv4(),
        },
        {
          type: 'grouping',
          value: ',',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'CGST AMOUNT',
          id: uuidv4(),
        },
        {
          type: 'grouping',
          value: ',',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'SGST AMOUNT',
          id: uuidv4(),
        },
        {
          type: 'grouping',
          value: ',',
          id: uuidv4(),
        },
        {
          type: 'field',
          value: 'IGST AMOUNT',
          id: uuidv4(),
        },
        {
          type: 'grouping',
          value: ')',
          id: uuidv4(),
        },
      ],
    },
  },
];
