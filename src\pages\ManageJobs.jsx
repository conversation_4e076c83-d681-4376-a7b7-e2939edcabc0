import { Tab } from '@headlessui/react';
import {
  ChevronDownIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import {
  Fragment,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import QRCode from 'react-qr-code';
import { useDispatch } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import BackIcon from '../assets/images/back.png';
import cross from '../assets/images/cross.png';
import { ReactComponent as JpgPng } from '../assets/svgs/pdfsvg.svg';
import { calculate } from '../calculateString';
import SideBarComponent from '../components/Jobs/ManageJobs/SideBarComponent';
import HistorySidebar from '../components/Kanban/HistorySidebar';
import CopyModal from '../components/ManageJobs/CopyModal';
import PriorityQueue from '../components/ManageJobs/PriorityQueue';
import SendQrModal from '../components/ManageJobs/SendQrModal';
import { default as QrModal } from '../pages/Modal';

import { useMediaQuery } from 'react-responsive';
import { default as PrintModal } from '../components/Jobs/ManageJobs/PrintModal';
import Button from '../components/global/components/Button';
import DragAndDrop from '../components/global/components/DragAndDrop';
import Header from '../components/global/components/Header';
import Input from '../components/global/components/Input';
import Modal from '../components/global/components/Modal';
import MultiSelect from '../components/global/components/MultiSelect';
import NewSelect from '../components/global/components/NewSelect';
import Pagination from '../components/global/components/Pagination';
import RightSidebar from '../components/global/components/RightSidebar';
import Select from '../components/global/components/Select';
import Spinner from '../components/global/components/Spinner';
import Table from '../components/global/components/Table';
import Tooltip from '../components/global/components/ToolTip';
import Label from '../components/v2/global/components/Label';
import {
  FormatDate,
  getLocalDateTime,
  mobileWidth,
  unCamelCaseString,
} from '../helperFunction';
import usePrefixIds from '../hooks/usePrefixIds';
import {
  useAddToQueueMutation,
  useGetQueueQuery,
} from '../slices/JobPriorityQueueApiSlice';
import { apiSlice } from '../slices/apiSlice';
import { useGetSelectedBomFormItemNamesMutation } from '../slices/assemblyBomApiSlice';
import {
  useArchiveCompletedMutation,
  useArchiveCreateInputMutation,
  useDeleteCreateInputByIDMutation,
  useLazyJobprogressQuery,
  usePostCreateInputMutation,
  useQueryAllProjectsQuery,
} from '../slices/createInputApiSlice';
import {
  useCreatePoMutation,
  useLazyGetPoByIdForJobQuery,
  useLazyGetPosWithoutPopulateQuery,
} from '../slices/createPoApiSlice';
import { useGetManageJobsFiletrsQuery } from '../slices/filterApiSlice';
import { useSendReportMutation } from '../slices/sendReportApiSlice';
import { Store } from '../store/Store';
import {
  DEFAULT_MULTIPLIER_VALUE,
  MAX_CHAR_ALLOWED,
  PAGINATION_LIMIT,
} from '../utils/Constant';
import { getstrLen } from '../utils/Getstrlen';
import { customConfirm } from '../utils/customConfirm';
import { isInternetConnected } from '../utils/isInternetConnected';
import SelectIcon from './../assets/images/SelectAll.png';
import RemoveIcon from './../assets/images/remove.png';
import JobProgressReportSummary from './JobprogressReport/JobProgressReportSummary';

const selectedStyle =
  'px-4 py-2 text-sm font-medium text-gray-900 bg-gray-100 outline-none w-full';
const nonSelectedStyle =
  'px-4 py-2 text-sm font-medium text-gray-900 bg-white outline-none w-full';

const btnStyling =
  'bg-gray-50 rounded-md text-gray-button px-4 text-sm h-[2.3rem] border border-gray-200 flex items-center gap-1 hover:bg-gray-100 mb-3 transition-all ease-in-out duration-300';

const ArchivedButton = ({
  className = '',
  archived,
  selectedCreateInputs,
  setSelectedCreateInputs,
}) => {
  const dispatch = useDispatch();
  const [expand, setExpand] = useState(false);

  const [archiveCompleted] = useArchiveCompletedMutation();
  const [archiveCreateInput] = useArchiveCreateInputMutation();

  const handleButtonClick = async () => {
    if (selectedCreateInputs?.length === 0) {
      toast.warn(
        `Please select a project to ${!archived ? 'archive' : 'activate'}`,
        { position: 'top-right', toastId: 'selectedCiHandler warn' }
      );
      return;
    }

    try {
      if (!archived) {
        // Add to archive
        await archiveCreateInput({
          mode: 'archive',
          data: selectedCreateInputs,
        }).unwrap();
        toast.success(`Projects archived: ${selectedCreateInputs?.length}`, {
          position: 'top-right',
          toastId: 'selectedCiHandler success',
        });
        setSelectedCreateInputs([]);
      } else {
        // Remove from archive
        await archiveCreateInput({
          mode: 'unarchive',
          data: selectedCreateInputs,
        }).unwrap();
        toast.success(`Projects activated: ${selectedCreateInputs?.length}`, {
          position: 'top-right',
          toastId: 'selectedCiHandler success',
        });
        setSelectedCreateInputs([]);
      }
      dispatch(apiSlice.util.invalidateTags(['CreateInputs']));
    } catch (err) {
      toast.error(
        err?.response?.data?.message ||
          err?.data?.message ||
          err?.message ||
          'Something went wrong! Please try again later',
        { position: 'top-right', toastId: 'selectedCiHandler error' }
      );
    }
  };

  const handleCompletedClick = async () => {
    try {
      const res = await archiveCompleted().unwrap();
      if (res) {
        if (res?.nModified === 0) {
          toast.warn(`There are no completed projects to archive`, {
            position: 'top-right',
            toastId: 'archiveCompleted warn',
          });
        } else {
          toast.success(`Projects archived: ${res?.nModified}`, {
            position: 'top-right',
            toastId: 'archiveCompleted success',
          });
          dispatch(apiSlice.util.invalidateTags(['CreateInputs']));
        }
      }
    } catch (err) {
      toast.error(
        err?.response?.data?.message ||
          err?.data?.message ||
          err?.message ||
          'Something went wrong! Please try again later',
        { position: 'top-right', toastId: 'archiveCompleted error' }
      );
    }
  };

  return (
    <div
      className={`relative ${className}`}
      onClick={() => setExpand((prev) => !prev)}
    >
      <button
        className={`bg-[#ec4899] rounded-md !text-white font-medium px-4 text-[13px] !h-[28px]   flex items-center gap-1  transition-all ease-in-out duration-300`}
        onClick={handleButtonClick}
      >
        {!archived ? 'Archive' : 'Activate'}
        <ChevronDownIcon className="h-4 w-4 ml-2 !text-white" />
      </button>
      {expand && (
        <ul className="absolute right-0 top-full w-full border bg-white text-black rounded-md overflow-hidden mt-1 ">
          <li
            className="px-2 py-1 hover:bg-blue-light cursor-pointer"
            onClick={handleButtonClick}
          >
            {!archived ? ' Selected' : ' Selected'}
          </li>
          <li
            className="px-2 py-1 hover:bg-blue-light cursor-pointer"
            onClick={handleCompletedClick}
          >
            Completed
          </li>
        </ul>
      )}
    </div>
  );
};

const ManageJobs = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isTrue, setIsTrue] = useState(false);
  const [qrData, setQrData] = useState('');
  const [isOpenQr, setIsOpenQr] = useState(false);
  const [idType, setIdtype] = useState('');
  const [historySidebar, setHistorySidebar] = useState({
    open: false,
    steps: [],
  });
  const [showSendQr, setShowSendQr] = useState(false);
  const [sendQrData, setSendQrData] = useState('');
  const [isOpenFor, setIsOpenFor] = useState('');

  const [selectedQrTab, setSelectedQrTab] = useState('Job QR');

  const [filters, setFilters] = useState({
    pros: [],
    models: [],
  });
  const [selectedCreateInputs, setSelectedCreateInputs] = useState([]);
  const [openSideBar, setOpenSideBar] = useState(false);
  const [sideBarData, setSideBarData] = useState({
    jobId: '',
    jobNo: '',
    name: '',
    modelName: '',
    productionFlow: '',
    id: '',
  });
  const [projectPanelData, setProjectPanelData] = useState({
    data: '',
    sameModelName: '',
  });
  const [ShowModal, setShowModal] = useState(false);
  const [ShowPasswordModal, setShowPasswordModal] = useState(false);
  const [JobSelectedForDelete, setJobSelectedForDelete] = useState(null);
  const [MasterPassword, setMasterPassword] = useState('');
  const [JobData, setJobData] = useState(null);
  const [CurrentProjects, setCurrentProjects] = useState([]);
  const [Email, setEmail] = useState('');
  const [EmailBody, setEmailBody] = useState('');
  const [JobProgress, setJobProgress] = useState(0);
  const [CompletionDate, setCompletionDate] = useState('N/A');
  const [SelectedFields, setSelectedFields] = useState([]);
  const [OpenSendReportModal, setOpenSendReportModal] = useState(false);
  const [TableData, setTableData] = useState('');
  const [SummaryData, setSummaryData] = useState('');
  const TableRef = useRef();
  const SummaryRef = useRef();
  const ProgressReportFields = [
    'Batch Name',
    'Batch Size',
    'Items/Hour',
    'Output',
    'Machine',
    'Worker',
    'Process Name',
    'Start Date',
    'Stop Date',
    'Duration',
    'Summary',
  ];

  const [isCopyModalOpen, setIsCopyModalOpen] = useState(false);
  const [selectedCopyProject, setSelectedCopyProject] = useState(null);
  const [openPriority, setOpenPriority] = useState(false);
  const [priorityQueue, setPriorityQueue] = useState([]);

  const {
    IdGenComp,
    idCompData,
    currentPrefixId: workOrderId,
  } = usePrefixIds({
    idFor: 'workOrderId',
  });

  const jobPrefix = usePrefixIds({
    idFor: 'jobId',
    additionalIdData: { WorkOrderId: workOrderId },
  });
  const modelPrefix = usePrefixIds({
    idFor: 'modelName',
    additionalIdData: { WorkOrderId: workOrderId },
  });

  const handleCopyClick = (num) => {
    setSelectedCopyProject(num);
    setIsCopyModalOpen((prev) => !prev);
  };
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCopyLoading, setIsCopyLoading] = useState(false);
  const [pdfname, setpdfname] = useState('');
  const {
    state,
    mqttClient,
    refreshParams: { refreshUrl, refreshPayload },
    defaults,
  } = useContext(Store);

  const { data: filtersData } = useGetManageJobsFiletrsQuery();
  const [pdf, setpdf] = useState([]);
  const [currentModalStep, setCurrentModalStep] = useState('select');
  const [serachParams, setSearchParams] = useSearchParams({
    page: 1,
    limit: PAGINATION_LIMIT,
    archived: false,
    job_id: '',
    fields: '',
    email: '',
  });
  const [selectedPo, setSelectedPo] = useState('');
  const [poData, setPoData] = useState({});
  const [selectedJob, setSelectedJob] = useState([]);
  const [selectedBomItem, setSelectedBomItem] = useState([]);

  const isMobile = useMediaQuery({ query: mobileWidth });

  const page = +serachParams?.get('page');
  const limit = +serachParams?.get('limit');
  const po = serachParams?.get('po') || '';
  const pro = serachParams?.get('pro') || '';
  const model = serachParams?.get('model') || '';
  const archived = serachParams?.get('archived') === 'true' || false;

  const {
    data: ciRes = {},
    isFetching: isFetchingQuery,
    isLoading: isLoadingQuery,
  } = useQueryAllProjectsQuery(
    {
      page,
      limit,
      po,
      pro,
      model,
      archived,
    },
    { skip: !page || !limit, refetchOnMountOrArgChange: true }
  );

  const {
    results: createInputs = [],
    totalPages = 0,
    totalResults = 0,
  } = ciRes;

  const [postCreateInput] = usePostCreateInputMutation();

  const [jobProgress] = useLazyJobprogressQuery();

  const [createPo] = useCreatePoMutation();
  const [getSelectedBomFormItemNames] =
    useGetSelectedBomFormItemNamesMutation();

  const [getPosWithoutPopulate, { data: allWorkOrder }] =
    useLazyGetPosWithoutPopulateQuery();

  const [getPoForJobById] = useLazyGetPoByIdForJobQuery();

  const { data: queueData } = useGetQueueQuery();
  const [addToQueue] = useAddToQueueMutation();

  const [sendReport] = useSendReportMutation();

  const [deleteCreateInputByID] = useDeleteCreateInputByIDMutation();

  useEffect(() => {
    if (filtersData && po) {
      const tempPo = filtersData?.find((item) => item._id === po);
      setFilters((prev) => ({
        ...prev,
        pros: [
          ...new Set(
            tempPo?.createInput
              ?.filter((ci) => ci.archived === archived)
              ?.map((ci) => ci.name)
          ),
        ],
      }));
    }
  }, [filtersData, po, archived]);

  useEffect(() => {
    getPosWithoutPopulate();
  }, [getPosWithoutPopulate]);

  useEffect(() => {
    if (queueData) {
      setPriorityQueue(queueData || []);
    }
  }, [queueData]);

  useEffect(() => {
    if (filtersData && po && pro) {
      const tempPo = filtersData?.find((item) => item._id === po);
      const tempPros = tempPo?.createInput?.filter(
        (ci) => ci.name === pro && ci.archived === archived
      );

      setFilters((prev) => ({
        ...prev,
        models: [...new Set(tempPros?.map((ci) => ci.modelName))],
      }));
    }
  }, [filtersData, po, pro, archived]);

  const copyProject = async (project, isWorkderCopy = false) => {
    try {
      // map over all goals table
      const goalsTables = project?.goalsTable?.map((tgt) => {
        const gt = { ...tgt };
        // delete _id property from goalstable
        if (gt?._id) delete gt?._id;
        // goals data of first process used for every goals table goalsdata
        const goalsData = project?.goalsTable?.[0]?.goalsData;
        // create tabledata by mapping over it and removing dates and status
        let tableData = project?.goalsTable?.[0]?.tableData.map((data) => {
          // looped table data to get params of current goalstable table data
          const loopedTableData = gt.tableData.find(
            (tData) => tData.batchNo === data.batchNo
          );

          // temp object to store multiprocess data
          let temp = {};
          // check if cureent process has subProcessData which indicates that table is multiprocess
          const check = Object.keys(gt?.tableData?.[0])?.includes(
            'subProcessData'
          );

          // if is multi process add subprocess data to temp which will be spread while returning object
          if (check) {
            temp.subProcesses = data.subProcesses;
            temp.subProcessData = data.subProcessData?.map((sData, sIdx) => {
              // looped subprocess data to get params of current table data subprocessdata

              const loopedSubProcessData = loopedTableData?.subProcessData.find(
                (i, idx) => idx === sIdx
              );

              return {
                ...loopedSubProcessData,
                'Batch Size': data?.['Batch Size'],
                newBatchSize: calculate(
                  `${data?.['Batch Size']}${
                    tgt?.multiplier || DEFAULT_MULTIPLIER_VALUE
                  }`
                ),
                process: sData.process,
                batchNo: sData?.batchNo,
                batchName: sData?.batchName,
                Speed: loopedSubProcessData?.Speed,
                Time: loopedSubProcessData?.Time,
                itemsPerHour: loopedSubProcessData?.itemsPerHour,
                changeOverTime: loopedSubProcessData?.changeOverTime,
                status: null,
                startDate: '',
                stopDate: '',
              };
            });
          }

          return {
            ...loopedTableData,
            'Batch Size': data?.['Batch Size'],
            newBatchSize: calculate(
              `${data?.['Batch Size']}${
                tgt?.multiplier || DEFAULT_MULTIPLIER_VALUE
              }`
            ),
            batchNo: data?.batchNo,
            batchName: data?.batchName,
            batchNameFormat: data?.batchNameFormat,
            Speed: loopedTableData?.Speed,
            Time: loopedTableData?.Time,
            itemsPerHour: loopedTableData?.itemsPerHour,
            changeOverTime: loopedTableData?.changeOverTime,
            disabled: false,
            status: null,
            startDate: '',
            stopDate: '',
            ...temp,
          };
        });
        return {
          ...gt,
          goalsData,
          tableData,
        };
      });

      if (goalsTables.length > 0) {
        let data = {
          assemblyItemToUpdate: project?.bomItemId,
          type: 'WorkOrder',
          alldata: project.alldata,
          goalsData: project.goalsData,
          id: project.id,
          inputScreen: project?.inputScreen?.id || project?.inputScreen,
          name: project.name,
          modelName: project.modelName,
          processGoalsLink: project.processGoalsLink,
          imageURLs: project.imageURLs,
          createPo: project?.createPo?._id || project?.createPo,
          productionFlow: project.productionFlow,
          inputScreenTableData: project?.inputScreenTableData,
          multiProcessData: project?.multiProcessData,
          linkedForms: project?.linkedForms,
          linkedAssemblyForms: project?.linkedAssemblyForms,
          goalsTable: goalsTables,
          jobCompletionQcForm: project?.jobCompletionQcForm || [],
          idsData: {
            jobIdData: { idData: jobPrefix?.idCompData?.dataToReturn },
            modelIdData: { idData: modelPrefix?.idCompData?.dataToReturn },
            batchIdData: {},
            additionalIdData: {
              WorkOrderId: workOrderId,
              JobId: jobPrefix?.currentPrefixId,
              ModelName: modelPrefix?.currentPrefixId,
              InputScreen: project?.name,
            },
          },
        };

        if (project?.assemblyProduct) {
          data = {
            ...data,
            assemblyPart: project?.assemblyProduct,
          };
        } else if (project?.assemblyPart) {
          data = {
            ...data,
            assemblyProduct: project?.assemblyPart,
          };
        } else {
          data = {
            ...data,
            assemblyManualEntry: project?.assemblyManualEntry,
          };
        }

        await postCreateInput({ data, mode: 'copy' }).unwrap();

        if (!isWorkderCopy) {
          toast.success('Job copied successfully!', {
            theme: 'colored',
            position: 'top-right',
          });
          dispatch(apiSlice.util.invalidateTags(['CreateInputs']));
          if (mqttClient?.publish)
            mqttClient?.publish(refreshUrl, refreshPayload);
        }
      }

      // -------------------------
    } catch (err) {
      toast.error(err?.response?.data?.message || err.message, {
        theme: 'colored',
        position: 'top-right',
      });
    }
  };

  const inputChangeHandler = (e) => {
    setSearchParams(
      (prev) => {
        prev.set(e.target.name, e.target.value);
        return prev;
      },
      { replace: true }
    );
  };

  const changeHandler = (e) => {
    for (let i in e) {
      let fname = e[i].name;
      let ftype = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;
        let data = {
          name: fname,
          type: ftype,
          data: url,
        };
        setpdf((prev) => [...prev, data]);
      });
    }
  };

  const removePdf = (e) => {
    const filtered = pdf.filter((item) => item.name !== e);
    setpdf(filtered);
  };

  const handleSubmit = async () => {
    try {
      if (!selectedPo) {
        toast.error('Please select a work order', {
          theme: 'colored',
          position: 'top-right',
        });
      } else {
        const targettedWorkOrder = poData;

        if (targettedWorkOrder?.createInput?.length > 0) {
          setIsCopyLoading(true);
          for (let i = 0; i < targettedWorkOrder?.createInput?.length; i++) {
            let project = createInputs?.find(
              (item) => item?._id === targettedWorkOrder?.createInput[i]?._id
            );

            await copyProject(
              project || targettedWorkOrder?.createInput?.[i],
              true
            );
          }

          dispatch(apiSlice.util.invalidateTags(['CreateInputs']));
          if (mqttClient?.publish)
            mqttClient?.publish(refreshUrl, refreshPayload);
          setIsCopyLoading(false);
          setIsModalOpen(false);
          setCurrentModalStep('select');
          setSelectedPo('');
          toast.success('Copied Workorder successfully', {
            position: 'top-right',
          });
        }
      }
    } catch (error) {
      setIsCopyLoading(false);
    }
  };

  const handleSelect = (e) => {
    if (e.target.checked) {
      setSelectedFields([
        ...SelectedFields,
        e.target.dataset.value.toLowerCase(),
      ]);
    } else if (!e.target.checked) {
      const find = SelectedFields.indexOf(e.target.dataset.value.toLowerCase());
      if (find === -1) return;
      SelectedFields.splice(find, 1);
      setSelectedFields([...SelectedFields]);
    }
  };

  const handleSelectAll = () => {
    const allFields = ProgressReportFields.map((field) => {
      return field.toLowerCase();
    });
    setSelectedFields(['', ...allFields]);
  };

  const handleRemoveAll = () => {
    setSelectedFields(['']);
  };

  const isFieldSelected = (fieldname) => {
    return SelectedFields?.includes(fieldname?.toLowerCase());
  };
  const ShowCorrectData = (current_table, current_batch) => {
    let data = null;
    CurrentProjects?.forEach((each) => {
      if (
        each?.flowId === current_table?.flowId &&
        each?.batchInfo?.batchName === current_batch?.batchName
      ) {
        data = each;
      }
    });
    return data;
  };
  const SendReport = async () => {
    if (!isInternetConnected()) {
      return;
    }
    if (!Email) {
      toast.error('Email is required', {
        position: 'top-right',
        theme: 'colored',
        toastId: 'email error',
      });
      return;
    }
    setSearchParams(
      (prev) => {
        prev.set('email', Email);
        return prev;
      },
      {
        replace: true,
      }
    );
    const fd = new FormData();
    fd.append('email', Email);
    if (TableData) {
      fd.append('table', TableData);
    }
    fd.append('body', EmailBody);
    if (SummaryData) {
      fd.append('summary', SummaryData);
    }
    try {
      const data = await sendReport({ data: fd }).unwrap();
      if (data?.message) {
        toast.success(data?.data?.message, {
          position: 'top-right',
          theme: 'colored',
          toastId: 'report sent',
        });
        setOpenSendReportModal(false);
      }
    } catch (error) {
      toast.error(error?.message, {
        position: 'top-right',
        theme: 'colored',
        toastId: 'error',
      });
    }
    setEmail('');
    setEmailBody('');
    setTableData('');
  };
  const getJobProgress = () => {
    let Progress = 0;
    CurrentProjects?.forEach((each) => {
      const batchsize = each?.batchInfo['Batch Size'];
      each?.machineAndOperator?.forEach((item) => {
        Progress += (item?.manualStopData / batchsize) * 100;
      });
    });
    setJobProgress(Progress);
  };
  const getCompletionTime = () => {
    const last_item =
      JobData?.goalsTable[JobData?.goalsTable?.length - 1]?.tableData[
        JobData?.goalsTable[JobData?.goalsTable?.length - 1]?.tableData
          ?.length - 1
      ];
    if (!last_item?.stopDate) return;
    setCompletionDate(last_item?.stopDate);
  };
  const SpecificJobDetails = async () => {
    const data = await jobProgress({
      id: serachParams?.get('job_id'),
    }).unwrap();
    if (data?.result) {
      setJobData(data?.result);
      setCurrentProjects(data?.result?.cuProjects);
    }
  };
  const getDetails = (id) => {
    setSearchParams(
      (prev) => {
        prev.set('job_id', id);
        return prev;
      },
      { replace: true }
    );
    setShowModal(true);
    SpecificJobDetails();
  };
  const OpenPrint = () => {
    setTableData(TableRef?.current?.innerHTML);
    setSummaryData(SummaryRef?.current?.innerHTML);
    if (SelectedFields.length <= 0) {
      toast.error('Please select at least one field', {
        position: 'top-right',
        theme: 'colored',
        toastId: 'field not selected',
      });
      return;
    }
    if (serachParams?.get('email')) {
      setEmail(serachParams?.get('email'));
    }
    setShowModal(false);
    setOpenSendReportModal(true);
  };
  window.onafterprint = () => {
    setShowModal(false);
    setIdtype('');
    if (SelectedFields.length > 0) {
      setOpenSendReportModal(false);
    }
  };
  useEffect(() => {
    getCompletionTime();
    getJobProgress();
    // eslint-disable-next-line
  }, [JobData, CurrentProjects]);
  useEffect(() => {
    setSelectedFields(serachParams?.get('fields').split(','));
    // eslint-disable-next-line
  }, []);
  useEffect(() => {
    if (SelectedFields.length === 0) return;
    setSearchParams(
      (prev) => {
        prev.set('fields', SelectedFields?.join(','));
        return prev;
      },
      { replace: true }
    );
    // eslint-disable-next-line
  }, [SelectedFields]);

  const addToPriorityQueue = async (jobId, jobName) => {
    try {
      await addToQueue({ jobId }).unwrap();
      toast.success(`Job ${jobName} Added to Queue`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'added',
      });
    } catch (e) {
      toast.error(`Error while adding ${jobName} to queue`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Name len error',
      });
    }
  };

  const allSelectedJobs = useMemo(() => {
    return selectedJob?.map((el) => {
      return createInputs.find((currElem) => currElem?._id === el?.value);
    });
  }, [selectedJob, createInputs]);

  const handleNewCopySubmit = async () => {
    if (getstrLen(pdfname) > MAX_CHAR_ALLOWED) {
      toast.error(`PDF name cannot exceeds ${MAX_CHAR_ALLOWED} characters`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'PDF name len error',
      });
      return;
    }
    const pdfNameExists = allWorkOrder.some((item) => item.name === pdfname);

    if (pdfNameExists) {
      toast.error('PDF name already exists in some other record.', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'PDF name already exists',
      });
      return;
    }

    if (!selectedPo) {
      toast.error('Invalid Workorder selected', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Invalid Workorder selected',
      });
      return;
    }

    if (!workOrderId) {
      toast.error('Work order ID is required');
      return;
    }

    setIsCopyLoading(true);
    const res = await createPo({
      data: { name: pdfname, files: pdf },
    }).unwrap();

    if (res) {
      const _id = res?.createdPo?._id;

      if (allSelectedJobs?.length > 0) {
        setIsCopyLoading(true);
        for (let i = 0; i < allSelectedJobs?.length; i++) {
          const newProject = {
            ...allSelectedJobs?.[i],
            createPo: _id,
          };

          await copyProject(newProject, true);
        }
        dispatch(apiSlice.util.invalidateTags(['CreateInputs']));
        if (mqttClient?.publish)
          mqttClient?.publish(refreshUrl, refreshPayload);
        setIsCopyLoading(false);
        setIsModalOpen(false);
        setpdfname('');
        setSelectedPo('');
        setSelectedJob([]);
        setpdf([]);
        setCurrentModalStep('select');
        toast.success('Copied jobs successfully');
        // setCopyId('');
        // setWorkOrderIdFormat({});
      } else {
        setIsCopyLoading(false);
        setIsModalOpen(false);
        setpdfname('');
        setSelectedPo('');
        setpdf([]);
        toast.success('Copied workorder successfully');
        // setCurrentModalStep('select');
        // setCopyId('');
        // setWorkOrderIdFormat({});
      }
    }
  };

  const Deletejob = async () => {
    if (!MasterPassword) {
      return toast.error('Master Password Is Required');
    }

    const confirm = await customConfirm(
      'Are you sure you want to delete this job?',
      'delete'
    );
    if (!confirm) return;

    const data = await deleteCreateInputByID({
      id: JobSelectedForDelete?._id,
      masterpassword: MasterPassword,
    }).unwrap();
    if (data) {
      if (JobSelectedForDelete?.status === 'completed') {
        toast.success('Job Hidden Successfully');
      } else {
        toast.success('Job Deleted Successfully');
      }
      setMasterPassword('');
      setShowPasswordModal(false);
      setJobSelectedForDelete(null);
    }
  };

  const allCreateInputs = useMemo(() => {
    return createInputs
      .filter((el) => el?.createPo?._id || el?.createPo === selectedPo)
      .map((ci) => ({
        label: ci.id !== '' ? `${ci.modelName}-${ci.id}` : ci.modelName,
        value: ci._id,
      }));
  }, [selectedPo, createInputs]);

  // const { data: manageJobData } = useGetManageJobDataQuery(); //eslint-disable-line

  const handlePrintQR = () => {
    // const printSection = document.getElementById('print-only');

    // if (printSection) {
    window.print();
    // }
  };

  const setSteps = (taskId) => {
    let tasks = state?.allTiles;
    let chosenTask = tasks?.find((elem) => elem?.taskId === taskId);
    if (chosenTask) {
      setHistorySidebar({
        open: true,
        steps: chosenTask?.steps,
        orderId: chosenTask?._id,
      });
    }
  };

  useEffect(() => {
    if (createInputs?.length > 0) {
      let bomItemIds = createInputs
        ?.map((item) => item?.bomItemId)
        .filter((item) => item !== '' && item !== undefined)
        .filter((item, index, self) => self.indexOf(item) === index);

      if (bomItemIds?.length > 0) {
        getSelectedBomFormItemNames({
          data: {
            ids: bomItemIds,
          },
        })
          .unwrap()
          .then((res) => {
            if (res?.error) {
              toast.error(res?.error);
            } else {
              // setSelectedBomItem(res);
              let inhouses = [];
              createInputs?.map?.((ci) => {
                if (ci?.createPo?.inhouse?.length > 0) {
                  inhouses.push(...ci?.createPo?.inhouse);
                }
                if (ci?.createPo?.items?.length > 0) {
                  inhouses.push(...ci?.createPo?.items);
                  ci?.createPo?.items?.forEach((el) => {
                    if (el?.bom?.item?.length > 0) {
                      inhouses.push(...el?.bom?.item);
                    }
                  });
                }
              });

              setSelectedBomItem([...res, ...inhouses]);
            }
          });
      }
    }
  }, [createInputs]); //eslint-disable-line

  return (
    <>
      <HistorySidebar sidebar={historySidebar} setSidebar={setHistorySidebar} />
      {showSendQr && (
        <SendQrModal
          sendQrData={sendQrData}
          setShowSendQr={setShowSendQr}
          selectedQrTab={selectedQrTab}
          setSelectedQrTab={setSelectedQrTab}
          isOpenQr={isOpenQr}
          setIsOpenQr={setIsOpenQr}
        />
      )}

      {isOpenFor === 'printJob' && (
        <>
          <PrintModal
            setQrData={setQrData}
            qrData={qrData}
            isOpenFor={isOpenFor}
            setIsOpenFor={setIsOpenFor}
          />
        </>
      )}

      {isOpenQr && (
        <QrModal
          className={'w-[90%] h-[90%]'}
          handleClose={() => {
            setShowSendQr(false);
            setIsOpenQr(false);
          }}
        >
          <div className="w-full">
            <Tab.Group
              as={'div'}
              className="mt-5 mb-2 px-6"
              selectedIndex={['Job QR', 'Unit QR'].findIndex(
                (tab) => tab === selectedQrTab
              )}
            >
              <Tab.List
                as={'div'}
                className="inline-flex rounded-lg shadow-sm overflow-hidden border border-[#d0d5dd] gap-x-px bg-[#d0d5dd] w-full h-[3%]"
              >
                {['Job QR', 'Unit QR'].map((tab) => {
                  return (
                    <Tab as={Fragment} key={tab}>
                      {({ selected }) => (
                        <button
                          className={
                            selected ? selectedStyle : nonSelectedStyle
                          }
                          onClick={() => {
                            setSelectedQrTab(tab);
                            if (tab === 'Job QR') {
                              setShowSendQr(false);
                              setIsOpenQr(true);
                            } else if (tab === 'Unit QR') {
                              setIsOpenQr(false);
                              setShowSendQr(true);
                            }
                          }}
                        >
                          {tab}
                        </button>
                      )}
                    </Tab>
                  );
                })}
              </Tab.List>
            </Tab.Group>
          </div>
          <div
            className="flex flex-col justify-center items-center gap-5 w-full h-full overflow-scroll"
            id={idType === 'print-only' ? 'print-only' : ''}
          >
            <div className="w-full !mb-4 flex flex-col">
              <div
                className={`flex flex-wrap ${idType === 'print-only' ? '' : 'justify-center'}`}
              >
                {qrData?.goalsTable[0]?.tableData?.map(
                  (eachgoalData, index) => {
                    return (
                      <div
                        key={index}
                        className="mt-2 flex w-[18%] flex-col items-center"
                      >
                        <QRCode
                          size={100}
                          value={JSON.stringify({
                            modelName: qrData?.modelName,
                            project: qrData?._id,
                            // process: Processname?.processName,
                            batchNo: (eachgoalData?.batchNo).toString(),
                            productionFlow: qrData?.productionFlow?._id,
                            // flowId: goalData?.flowId,
                            // type: Processname?.processCategory,
                            qrFor: 'job',
                          })}
                        />
                        <div className="flex flex-col items-center">
                          <p className="text-xs font-normal ">
                            Model Name: {`${qrData?.modelName}`}
                          </p>
                          {/* <p className="text-xs font-normal ">
                            Process Name: {`${Processname?.processName}`}
                          </p> */}
                          <p className="text-xs font-normal">
                            Batch No : {`${eachgoalData?.batchNo}`}
                          </p>
                        </div>
                      </div>
                    );
                  }
                )}
              </div>
            </div>

            <div className="flex flex-row gap-12 print:hidden" id="ignore">
              <button
                className={btnStyling}
                onClick={() => {
                  setIsOpenQr(false);
                  setShowSendQr(false);
                }}
              >
                Close
              </button>
              <button
                className={btnStyling}
                onClick={() => {
                  setIdtype('print-only');
                  setTimeout(() => {
                    handlePrintQR();
                  }, 500);
                }}
              >
                Print
              </button>
            </div>
          </div>
        </QrModal>
      )}

      {ShowPasswordModal && (
        <Modal
          title={'Enter Master Password'}
          description={'Enter Your Master Password To Delete The Job'}
          onCloseModal={() => {
            setShowPasswordModal((prev) => {
              return !prev;
            });
          }}
          onSubmit={Deletejob}
        >
          {() => {
            return (
              <>
                <section className="master-password">
                  <div className="input-wrapper">
                    <Label className="!text-[18px]">Master Password</Label>
                    <Input
                      type="password"
                      value={MasterPassword}
                      onChange={(e) => {
                        setMasterPassword(e.target.value);
                      }}
                    />
                  </div>
                </section>
              </>
            );
          }}
        </Modal>
      )}

      {isModalOpen && (
        <Modal
          svg={<InformationCircleIcon className="w-6 h-6" />}
          title="Copy Work Order"
          description="You can copy whole work order from here"
          onCloseModal={() => {
            setCurrentModalStep('select');
            setIsModalOpen(false);
            setpdfname('');
            setSelectedPo('');
            setpdf([]);
            // setCopyId('');
          }}
          onSubmit={
            currentModalStep === 'existing' ? handleSubmit : handleNewCopySubmit
          }
          btnIsLoading={isCopyLoading}
          isBackButton={
            currentModalStep === 'existing' || currentModalStep === 'new'
          }
          onBackClick={() => setCurrentModalStep('select')}
        >
          {() => {
            return (
              <>
                <div className="flex flex-row flex-wrap justify-start gap-x-7">
                  {currentModalStep === 'select' && (
                    <div className="flex items-center space-x-4">
                      <Button
                        onClick={() => setCurrentModalStep('existing')}
                        className={'!h-7 !text-xs md:text-sm'}
                      >
                        Copy as existing
                      </Button>
                      <Button
                        onClick={() => setCurrentModalStep('new')}
                        className={'!h-7 !text-xs md:text-sm'}
                      >
                        Copy as new
                      </Button>
                    </div>
                  )}

                  {currentModalStep === 'existing' && (
                    <div className="flex flex-col items-start w-full  md:w-1/2">
                      <div className="flex">
                        <label className="text-sm">Select Workorder</label>
                        <span className="text-xl text-red-500 -mt-1">*</span>
                      </div>
                      <NewSelect
                        options={allWorkOrder.map((el) => ({
                          name: `${el?.name} (${el?.workOrderId})`,
                          value: el?._id,
                        }))}
                        placeholder="Select workorder"
                        value={selectedPo}
                        onChange={(e) => {
                          setSelectedPo(e.target.value);
                          setPoData(
                            allWorkOrder.find(
                              (el) => el?._id === e.target.value
                            )
                          );
                        }}
                      />
                    </div>
                  )}

                  {currentModalStep === 'new' && (
                    <div className="w-full">
                      <div className="flex flex-col  md:justify-between w-full gap-3">
                        <label className="mb-1 text-base text-[#667085] w-1/3">
                          ID
                          <span className="text-xl text-red-500 -mt-2">*</span>
                          <IdGenComp {...idCompData} />
                        </label>
                        <div className="flex flex-col md:flex-row items-start   gap-3">
                          <div className="flex flex-col items-start w-full">
                            <div className="flex">
                              {' '}
                              <label className="text-sm">
                                Select Workorder
                              </label>
                              <span className="text-xl text-red-500 -mt-2 ml-1">
                                *
                              </span>
                            </div>
                            <NewSelect
                              options={allWorkOrder?.map((el) => ({
                                name: `${el?.name} (${el?.workOrderId})`,
                                value: el?._id,
                              }))}
                              placeholder="Select workorder"
                              value={selectedPo}
                              onChange={async (e) => {
                                const res = await getPoForJobById({
                                  id: e.target.value,
                                }).unwrap();

                                const targettedWorkOrder = res;
                                if (targettedWorkOrder) {
                                  const formattedFiles =
                                    targettedWorkOrder?.files?.map((el) => ({
                                      data: el?.data,
                                      name: el?.name,
                                      type: el?.type,
                                    }));
                                  setpdf(formattedFiles);
                                }

                                setSelectedPo(e.target.value);
                                setPoData(
                                  allWorkOrder.find(
                                    (el) => el?._id === e.target.value
                                  )
                                );
                              }}
                            />
                          </div>
                          <div className="flex flex-col items-start w-full">
                            <label className="text-sm">Select Job</label>
                            <MultiSelect
                              options={allCreateInputs}
                              placeholder="Select job"
                              value={selectedJob}
                              onChange={(e) => setSelectedJob(e.target.value)}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col my-5 w-full">
                        <label className="mb-1 text-base text-[#667085]">
                          Name{' '}
                        </label>
                        <Input
                          name="name"
                          id="name"
                          className="md:w-3/5 md:mr-[20%]"
                          value={pdfname}
                          onChange={(e) => setpdfname(e.target.value)}
                          required
                        />
                      </div>
                      <div className="mt-2 w-full col-span-2">
                        <label className="mb-1 font-semibold text-[#b4b4b5]">
                          Select File
                        </label>
                        <DragAndDrop
                          accept="application/pdf"
                          fileType="JPG/PNG"
                          svg={<JpgPng className="h-16" />}
                          onChange={(e) => changeHandler(e, 'project')}
                          multiple
                          className={`text-[#667085]`}
                          disabled={pdfname === ''}
                        />
                      </div>{' '}
                      <Table className={`mt-5`}>
                        <Table.Head>
                          <Table.Row>
                            <Table.Th>Files</Table.Th>
                            <Table.Th></Table.Th>
                          </Table.Row>
                        </Table.Head>
                        <Table.Body>
                          {pdf?.map((item, idx) => {
                            return (
                              <Table.Row key={idx}>
                                <Table.Td>{item.name}</Table.Td>
                                <Table.Td
                                  onClick={() => {
                                    removePdf(item.name);
                                  }}
                                >
                                  <img src={cross} alt="" className="w-2 h-2" />
                                </Table.Td>
                              </Table.Row>
                            );
                          })}
                        </Table.Body>
                      </Table>
                    </div>
                  )}
                </div>
              </>
            );
          }}
        </Modal>
      )}
      <div className="w-full">
        {OpenSendReportModal && (
          <Modal
            title={'Send Progress Report'}
            description={'Job Name -' + JobData?.alldata[0]?.name}
            onCloseModal={() => {
              setOpenSendReportModal(false);
            }}
            onSubmit={SendReport}
          >
            {() => {
              return (
                <section className="main-contaienr">
                  <Button
                    className="mb-[1rem]"
                    onClick={() => {
                      setOpenSendReportModal(false);
                      setShowModal(true);
                    }}
                  >
                    <img
                      src={BackIcon}
                      alt="Back Icon"
                      className="w-5 h-5 object-contain relative"
                    />
                    Go Back
                  </Button>
                  <div className="email-input-container">
                    <label htmlFor="email" className="font-medium">
                      Email
                    </label>
                    <Input
                      placeholder="Enter Email"
                      required={true}
                      className="mb-[12px]"
                      value={Email}
                      onChange={(e) => {
                        setEmail(e.target.value);
                      }}
                    />
                    <label htmlFor="email" className="font-medium">
                      Description
                    </label>
                    <textarea
                      className={`border text-left text-black outline-none border-[#C8CEE1] bg-transparent rounded-lg py-2 px-3 w-full`}
                      rows="8"
                      value={EmailBody}
                      onChange={(e) => {
                        setEmailBody(e.target.value);
                      }}
                    ></textarea>
                  </div>
                </section>
              );
            }}
          </Modal>
        )}
        {/* Job progress report sending modal */}
        {ShowModal && (
          <Modal
            title={'Select fields for job report'}
            description={
              'Select the fields you want to show in the progress report'
            }
            onCloseModal={() => {
              setShowModal(false);
            }}
            onSubmit={OpenPrint}
          >
            {() => {
              return (
                <>
                  <div className="flex justify-start md:justify-end mt-4 mb-8 gap-8">
                    <Button
                      onClick={handleSelectAll}
                      className="!h-10 !text-xs md:text-sm md:w-1/4 md:min-w-[150px]"
                    >
                      <img
                        src={SelectIcon}
                        alt="Select All Icon"
                        className="w-5 h-5 object-contain relative"
                      />
                      Select All
                    </Button>
                    <Button
                      onClick={handleRemoveAll}
                      className="w-1/4 min-w-[150px] text-xs md:text-sm !h-10 bg-red-primary hover:bg-red-hover"
                    >
                      <img
                        src={RemoveIcon}
                        alt="Remove Icon"
                        className="w-5 h-5 object-contain relative"
                      />
                      Remove All
                    </Button>
                  </div>
                  {/* Section for selecing the fields that user want to show in the progress report */}
                  <section className="checkbox-container flex gap-[2rem] flex-wrap">
                    {ProgressReportFields?.map((each, key) => {
                      return (
                        <label
                          htmlFor=""
                          className="text-[18px] flex justify-center items-center gap-x-2"
                          key={key}
                        >
                          {each}
                          <input
                            type="checkbox"
                            checked={serachParams
                              ?.get('fields')
                              .split(',')
                              ?.includes(each.toLocaleLowerCase())}
                            data-value={each}
                            onChange={handleSelect}
                          />
                        </label>
                      );
                    })}
                  </section>

                  {isFieldSelected('summary') && (
                    <div
                      id="print-only"
                      className="job-progress-preview-data summary-data"
                      ref={SummaryRef}
                    >
                      <JobProgressReportSummary job={JobData} />
                    </div>
                  )}
                  <div
                    id="print-only"
                    className="job-progress-preview-data"
                    ref={TableRef}
                  >
                    {!isFieldSelected('summary') && (
                      <div
                        className="alldata-header"
                        style={{
                          width: '450px',
                          margin: 'auto',
                          columnGap: '15%',
                          marginBottom: '12px',
                          display: 'flex',
                        }}
                      >
                        <div>
                          <p
                            className="small-text"
                            style={{
                              marginBottom: '6px',
                              fontSize: '12px',
                              fontWeight: '500',
                              color: 'rgb(104, 104, 104)',
                            }}
                          >
                            Job Name
                          </p>
                          <p
                            className="big-text"
                            style={{
                              margin: '0px',
                              margiBottom: '16px',
                              fontSize: '26px',
                            }}
                          >
                            {JobData?.alldata[0]?.name}
                          </p>
                        </div>
                        <div>
                          <p
                            className="small-text"
                            style={{
                              marginBottom: '6px',
                              fontSize: '12px',
                              fontWeight: '500',
                              color: 'rgb(104, 104, 104)',
                            }}
                          >
                            Job Progress
                          </p>
                          <p
                            className="big-text"
                            style={{
                              margin: '0px',
                              margiBottom: '16px',
                              fontSize: '26px',
                            }}
                          >
                            {JobProgress}%
                          </p>
                        </div>
                        <div>
                          <p
                            className="small-text"
                            style={{
                              marginBottom: '6px',
                              fontSize: '12px',
                              fontWeight: '500',
                              color: 'rgb(104, 104, 104)',
                            }}
                          >
                            Expected Completion Date
                          </p>
                          <p
                            className="big-text"
                            style={{
                              margin: '0px',
                              margiBottom: '16px',
                              fontSize: '26px',
                            }}
                          >
                            {CompletionDate}
                          </p>
                        </div>
                      </div>
                    )}

                    <section
                      className="w-full overflow-x-scroll mt-10"
                      id="print-job-progress"
                    >
                      <Table className="mt-5">
                        <Table.Head>
                          {isFieldSelected('Batch Name') && (
                            <Table.Th className="min-w-[150px]">
                              Batch Name
                            </Table.Th>
                          )}
                          {isFieldSelected('Batch Size') && (
                            <Table.Th className="min-w-[150px]">
                              Batch Size
                            </Table.Th>
                          )}
                          {isFieldSelected('Items/Hour') && (
                            <Table.Th className="min-w-[150px]">
                              Items/Hour
                            </Table.Th>
                          )}
                          {isFieldSelected('Output') && (
                            <Table.Th className="min-w-[150px]">
                              Output
                            </Table.Th>
                          )}
                          {isFieldSelected('Machine') && (
                            <Table.Th className="min-w-[150px]">
                              Machine
                            </Table.Th>
                          )}
                          {isFieldSelected('Worker') && (
                            <Table.Th className="min-w-[150px]">
                              Worker
                            </Table.Th>
                          )}
                          {isFieldSelected('Process Name') && (
                            <Table.Th className="min-w-[150px]">
                              Process Name
                            </Table.Th>
                          )}
                          {isFieldSelected('Start Date') && (
                            <Table.Th className="min-w-[150px]">
                              Start Date
                            </Table.Th>
                          )}
                          {isFieldSelected('stop date') && (
                            <Table.Th className="min-w-[150px]">
                              Stop Date
                            </Table.Th>
                          )}
                          {isFieldSelected('Duration') && (
                            <Table.Th className="min-w-[150px]">
                              Duration
                            </Table.Th>
                          )}
                        </Table.Head>
                        <Table.Body>
                          {JobData?.goalsTable?.map((each) => {
                            return each?.tableData?.map((row, key) => {
                              return (
                                <Table.Row key={key}>
                                  {isFieldSelected('batch name') && (
                                    <Table.Td>{row?.batchName}</Table.Td>
                                  )}
                                  {isFieldSelected('batch size') && (
                                    <Table.Td>{row['Batch Size']}</Table.Td>
                                  )}
                                  {isFieldSelected('items/hour') && (
                                    <Table.Td>
                                      {row?.itemsPerHour} / Hour
                                    </Table.Td>
                                  )}
                                  {isFieldSelected('output') && (
                                    <Table.Td>
                                      {ShowCorrectData(each, row)
                                        ?.machineAndOperator[0]?.machine
                                        ?.isManual
                                        ? ShowCorrectData(each, row)
                                            ?.machineAndOperator[0]
                                            ?.manualStopData || 0
                                        : ShowCorrectData(each, row)
                                            ?.machineAndOperator[0]
                                            ?.calcStopCount || 0}
                                    </Table.Td>
                                  )}
                                  {isFieldSelected('machine') && (
                                    <Table.Td>
                                      {ShowCorrectData(each, row)
                                        ?.machineAndOperator[0]?.machine
                                        ?.machineName || 'N/A'}
                                    </Table.Td>
                                  )}
                                  {isFieldSelected('worker') && (
                                    <Table.Td>
                                      {ShowCorrectData(each, row)
                                        ?.machineAndOperator[0]?.operator[0]
                                        ?.user?.name || 'N/A'}
                                    </Table.Td>
                                  )}
                                  {isFieldSelected('process name') && (
                                    <Table.Td>
                                      {ShowCorrectData(each, row)?.mqtt
                                        ?.process || 'Process Not Started'}
                                    </Table.Td>
                                  )}
                                  {isFieldSelected('start date') && (
                                    <Table.Td>
                                      {FormatDate(row?.startDate) || 'N/A'}
                                    </Table.Td>
                                  )}
                                  {isFieldSelected('end date') && (
                                    <Table.Td>
                                      {FormatDate(row?.stopDate) || 'N/A'}
                                    </Table.Td>
                                  )}
                                  {isFieldSelected('duration') && (
                                    <Table.Td>
                                      {row?.duration || 'N/A'}
                                    </Table.Td>
                                  )}
                                </Table.Row>
                              );
                            });
                          })}
                        </Table.Body>
                      </Table>
                    </section>
                  </div>
                </>
              );
            }}
          </Modal>
        )}
        <RightSidebar
          openSideBar={openSideBar}
          setOpenSideBar={setOpenSideBar}
          className="!w-[60%]"
          scale={736}
        >
          <SideBarComponent
            projectPanelData={projectPanelData}
            setProjectPanelData={setProjectPanelData}
            sideBarDataId={sideBarData}
          />
        </RightSidebar>

        <CopyModal
          isOpen={isCopyModalOpen}
          handleClose={() => {
            setSelectedCopyProject(null);
            setIsCopyModalOpen(false);
            setpdfname('');
            setSelectedPo('');
            setpdf([]);
            setCurrentModalStep('select');
            // setCopyId('');
            // setWorkOrderIdFormat({});
          }}
          handleCopy={(data) => copyProject(data)}
          selectedCopyProject={selectedCopyProject}
          jobPrefix={jobPrefix}
          modelPrefix={modelPrefix}
        />

        <PriorityQueue
          openQueue={openPriority}
          setOpenQueue={setOpenPriority}
          priorityQueue={priorityQueue}
          setPriorityQueue={setPriorityQueue}
        />

        <div className="flex flex-col justify-between">
          <Header
            title="Manage Jobs"
            description=""
            hasInfoPopup
            infoTitle="Welcome to the Manage Job Page"
            infoDesc="Your space for comprehensive job planning in production."
            paras={[
              'Effortlessly manage and coordinate previously created jobs, project assignments, and material logistics',
              'Here, you can input job details based on your order specifications. Afterward, you have the flexibility to divide the order quantity into desired batches and set process goals for each batch. Process goals serve as ideal benchmarks for timelines and performance parameters during job execution.',
              'This page also allows you to attach media files and specific Bill of Materials for the job, and select production details to display on the Shop Floor Monitors.',
              'Simplify your job planning and streamline production with our user-friendly Create Job Page.',
            ]}
          />
        </div>

        <div className="w-800 flex justify-between mt-[-8px]">
          <div className="inline-flex rounded-lg shadow-sm overflow-hidden border border-[#d0d5dd] gap-x-px bg-[#d0d5dd] mb-5  w-full  md:w-1/5">
            <button
              type="button"
              onClick={() => {
                inputChangeHandler({
                  target: { name: 'archived', value: false },
                });
                setSelectedCreateInputs([]);
              }}
              className={`${
                !archived && '!bg-gray-200'
              } ml-[-10px] bg-white p-6 border-1 border-[#a8a29e] w-full ${
                !archived ? selectedStyle : nonSelectedStyle
              }`}
            >
              Active
            </button>
            <button
              type="button"
              onClick={() => {
                inputChangeHandler({
                  target: { name: 'archived', value: true },
                });

                setSelectedCreateInputs([]);
              }}
              className={` ${
                archived && '!bg-gray-200'
              } ml-[-10px] bg-white p-6 border-1 border-[#a8a29e] w-full ${
                !archived ? selectedStyle : nonSelectedStyle
              }`}
              //   className={archived ? selectedStyle : nonSelectedStyle}
            >
              Archived
            </button>
          </div>
        </div>

        <div className="flex justify-end  w-full bg-white rounded-tl-lg rounded-tr-lg mt-[-5px]">
          <div className="flex justify-end gap-3 md:gap-4 mb-2 relative mt-2 items-center ">
            {!isMobile && (
              <section className="relative group">
                <p
                  className=" w-21 !text-black text-[12px]"
                  onClick={() => setIsTrue(!isTrue)}
                >
                  <svg
                    width="21"
                    height="21"
                    viewBox="0 0 21 21"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="mr-1 cursor-pointer"
                  >
                    <path
                      d="M9.78744 20.3438H7.875V10.4109L0.984375 2.20779V0.65625H19.6875V2.19893L13.125 10.4021V17.0062L9.78744 20.3438ZM9.1875 19.0312H9.24381L11.8125 16.4626V9.9417L18.1908 1.96875H2.49785L9.1875 9.93284V19.0312Z"
                      fill="#56555C"
                    />
                  </svg>
                </p>
                <span className="absolute top-[2rem] !right-[-8px] transform -translate-x-1/2 mt-2 w-max px-2 py-1 text-xs text-white bg-black rounded opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                  Filter
                </span>

                {isTrue && (
                  <div className="absolute right-0 top-[110%] flex flex-col items-center w-[640px] z-10 bg-white border border-gray-200 shadow-md rounded-lg p-5">
                    <div className="flex w-full justify-around">
                      <section className="mr-[1rem] w-1/3 flex flex-col">
                        <label
                          className="mb-1"
                          style={{ fontSize: '13px', fontWeight: 'bold' }}
                          htmlFor="projectId"
                        >
                          Select Work Order
                        </label>
                        <Select
                          name="po"
                          value={po || ''}
                          onChange={inputChangeHandler}
                          options={filtersData?.map((filter) => ({
                            name: `${filter.name} (${filter.workOrderId})`,
                            value: filter._id,
                          }))}
                          placeholder="Select"
                          className="w-full mb-2"
                        />
                      </section>

                      <section className="mr-[1rem] w-1/3 flex flex-col">
                        <label
                          className="mb-1"
                          htmlFor="projectId"
                          style={{ fontSize: '13px', fontWeight: 'bold' }}
                        >
                          {
                            defaults?.defaultParam?.projectDefaults
                              ?.projectIdentifier
                          }
                        </label>
                        <Select
                          name="pro"
                          value={pro || ''}
                          onChange={inputChangeHandler}
                          options={filters?.pros?.map((i) => ({
                            name: i,
                            value: i,
                          }))}
                          placeholder="Select"
                          className="w-full mb-2"
                        />
                      </section>

                      <section className="mr-[1rem] w-1/3 flex flex-col">
                        <label
                          className="mb-1"
                          htmlFor="projectId"
                          style={{ fontSize: '13px', fontWeight: 'bold' }}
                        >
                          {defaults?.defaultParam?.projectDefaults?.modelLabel}
                        </label>
                        <Select
                          name="model"
                          value={model || ''}
                          onChange={inputChangeHandler}
                          options={filters?.models?.map((i) => ({
                            name: i,
                            value: i,
                          }))}
                          placeholder="Select"
                          className="w-full mb-2"
                        />
                      </section>
                    </div>
                    <Button
                      className="bg-red-600 hover:bg-red-reset w-32 p-3 text-sm transition duration-300 font-5"
                      onClick={() => {
                        setSearchParams((prev) => {
                          prev?.delete('po');
                          prev?.delete('pro');
                          prev?.delete('model');
                          return prev;
                        });
                      }}
                    >
                      Reset
                    </Button>
                  </div>
                )}
              </section>
            )}
            <ArchivedButton
              className={'!h-7 !text-[13px] xs:w-[8rem]'}
              archived={archived}
              selectedCreateInputs={selectedCreateInputs}
              setSelectedCreateInputs={setSelectedCreateInputs}
            />
            <Button
              onClick={() => setIsModalOpen(true)}
              className=" bg-[#38bdf8] !text-white w-16  md:w-[6rem] !h-7 text-xs md:!text-[13px] !list-none  "
            >
              <li className="cursor-pointer transition-colors duration-300 font-medium">
                Copy&nbsp;PO
              </li>
            </Button>
            <Button
              onClick={() => setOpenPriority(true)}
              className=" bg-[#a855f7] w-[110px] !px-0 md:w-[8rem] !h-7 text-xs  md:!text-[13px] mr-1  md:mr-4  !list-none"
            >
              <li className="cursor-pointer transition-colors duration-300 font-medium text-white">
                Priority Queue
              </li>
            </Button>
          </div>
        </div>

        {isLoadingQuery ? (
          <Spinner />
        ) : (
          <>
            <div className="w-full overflow-x-scroll">
              <Table className="w-full rounded-xl">
                <Table.Head className="rounded-xl">
                  <Table.Row className=" w-full rounded-xl">
                    {!isMobile && <Table.Th></Table.Th>}
                    {!isMobile && <Table.Th>Task ID</Table.Th>}
                    {!isMobile && <Table.Th>Date/Time</Table.Th>}
                    <Table.Th>Workorder Id</Table.Th>
                    <Table.Th>Item Name</Table.Th>
                    {!isMobile && (
                      <Table.Th>
                        {
                          defaults?.defaultParam?.projectDefaults
                            ?.projectIdentifier
                        }
                      </Table.Th>
                    )}
                    <Table.Th>
                      {defaults?.defaultParam?.projectDefaults?.modelLabel}
                    </Table.Th>
                    {!isMobile && <Table.Th>Batches</Table.Th>}
                    <Table.Th>Status</Table.Th>

                    {!isMobile && <Table.Th>QR</Table.Th>}
                    {!isMobile && <Table.Th></Table.Th>}
                    {/* <Table.Th>Send QR</Table.Th> */}
                    <Table.Th></Table.Th>
                  </Table.Row>
                </Table.Head>

                <Table.Body>
                  {createInputs.length === 0 && (
                    <Table.Row>
                      <Table.Td colSpan={'100%'}>
                        <p className="py-2 text-center">No project found</p>
                      </Table.Td>
                    </Table.Row>
                  )}
                  {createInputs.map((num) => {
                    if ('isVisible' in num) {
                      if (!num?.isVisible) {
                        return;
                      }
                    }
                    const noOfBatches = num?.goalsTable?.[0]?.tableData?.length;
                    let rank = -1;
                    if (priorityQueue) {
                      for (let elem of priorityQueue) {
                        if (num?._id === elem?.job?._id) rank = elem?.rank;
                      }
                    }

                    let status = null;
                    if (
                      num?.goalsTable?.[num?.goalsTable?.length - 1]
                        ?.tableData?.[
                        num?.goalsTable?.[num?.goalsTable?.length - 1]
                          ?.tableData?.length - 1
                      ]?.status === 'complete'
                    ) {
                      status = 'complete';
                    } else if (
                      num?.goalsTable?.[0]?.tableData?.[0]?.status === null
                    ) {
                      status = 'not started';
                    } else {
                      status = 'ongoing';
                    }

                    return (
                      <Table.Row
                        key={num._id}
                        isFetching={isFetchingQuery}
                        isClickable={true}
                      >
                        {!isMobile && (
                          <Table.Td>
                            <Input
                              type="checkbox"
                              className="w-fit h-fit m-auto cursor-pointer !bg-transparent"
                              inputClassname="!h-fit !p-0 !rounded-0"
                              disabled={num?.status === 'ongoing'}
                              checked={selectedCreateInputs?.includes(num._id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedCreateInputs((prev) => [
                                    ...prev,
                                    num._id,
                                  ]);
                                } else {
                                  setSelectedCreateInputs((prev) =>
                                    prev.filter(
                                      (project) => project !== num._id
                                    )
                                  );
                                }
                              }}
                            />
                          </Table.Td>
                        )}

                        {!isMobile && (
                          <Table.Td
                            className="hover:cursor-pointer hover:underline min-w-[50px] font-medium !text-blue-400"
                            onClick={() => {
                              setSteps(num?.taskId?.taskId);
                            }}
                          >
                            {num?.taskId?.customTaskId
                              ? `${num?.taskId?.customTaskId}(${num?.taskId?.taskId})`
                              : num?.taskId?.taskId || '-'}
                          </Table.Td>
                        )}
                        {!isMobile && (
                          <Table.Td
                            onClick={() => {
                              setOpenSideBar(true);
                              setSideBarData(num?._id);
                            }}
                          >
                            {' '}
                            {getLocalDateTime(num.createdAt) || '-'}
                          </Table.Td>
                        )}
                        <Table.Td
                          className="hover:cursor-pointer hover:underline md:min-w-[50px] font-medium !text-blue-400"
                          onClick={() => {
                            setOpenSideBar(true);
                            setSideBarData(num?._id);
                          }}
                        >
                          <span className="max-w-[5ch] break-words overflow-wrap">
                            {`${num?.createPo?.workOrderId || ''}(${num?.createPo?.name || '-'})`}
                          </span>
                        </Table.Td>

                        <Table.Td
                          className="hover:cursor-pointer hover:underline md:min-w-[45px] font-medium !text-blue-400"
                          onClick={() => {
                            setOpenSideBar(true);
                            setSideBarData(num?._id);
                          }}
                        >
                          {(() => {
                            let name = '';
                            if (num?.workOrderRef === 'WorkOrder') {
                              name =
                                selectedBomItem?.find(
                                  (i) => i?._id === num?.bomItemId
                                )?.itemId?.name ||
                                selectedBomItem?.find(
                                  (i) => i?.indexId === num?.bomItemId
                                )?.itemName;
                            } else {
                              name =
                                selectedBomItem?.find(
                                  (i) => i?._id === num?.bomItemId
                                )?.product?.name ||
                                selectedBomItem?.find(
                                  (i) => i?._id === num?.bomItemId
                                )?.part?.name ||
                                '-';
                            }

                            return (
                              <span className="max-w-[5ch] break-words overflow-wrap">
                                {name?.length > 55 ? (
                                  <Tooltip
                                    text={name}
                                    maxWidth={'!max-w-[500px]'}
                                    minWidth={'!min-w-[250px]'}
                                  >
                                    {name?.slice(0, 55) + '...'}
                                  </Tooltip>
                                ) : (
                                  name || '-'
                                )}
                              </span>
                            );
                          })()}
                          {/* <span className="max-w-[5ch] break-words overflow-wrap">
                            {`${selectedBomItem?.find((i) => i?._id === num?.bomItemId)?.product?.name || selectedBomItem?.find((i) => i?._id === num?.bomItemId)?.part?.name || '-'}`}
                          </span> */}
                        </Table.Td>

                        {!isMobile && (
                          <Table.Td
                            onClick={() => {
                              setOpenSideBar(true);
                              setSideBarData(num?._id);
                            }}
                          >
                            <p className="w-full flex justify-between items-center px-2">
                              <span>{num.id === ' ' ? '-' : num.id}</span>
                              {rank !== -1 && (
                                <span
                                  onClick={() => {
                                    setOpenPriority(true);
                                  }}
                                  className="ml-8 inline-block cursor-pointer bg-red-primary rounded text-white text-center w-[1.5rem] px-2 py-1"
                                >
                                  {rank}
                                </span>
                              )}
                            </p>
                          </Table.Td>
                        )}

                        <Table.Td
                          className={'md:!min-w-[12rem]'}
                          onClick={() => {
                            setOpenSideBar(true);
                            setSideBarData(num?._id);
                          }}
                        >
                          <p
                            className={`cursor-pointer text-blue-500 ${isMobile && 'max-w-[7ch] break-words overflow-wrap'}`}
                          >
                            {num.modelName?.length > 55 ? (
                              <Tooltip text={num.modelName}>
                                {num.modelName?.slice(0, 55) + '...'}
                              </Tooltip>
                            ) : num.modelName === ' ' ? (
                              '-'
                            ) : (
                              num.modelName || '-'
                            )}
                          </p>
                        </Table.Td>

                        {!isMobile && (
                          <Table.Td
                            onClick={() => {
                              setOpenSideBar(true);
                              setSideBarData(num?._id);
                            }}
                          >
                            {`#${
                              num?.projectNo > 1
                                ? num?.projectNo * noOfBatches -
                                  (noOfBatches - 1)
                                : num?.projectNo
                            } - #${num?.projectNo * noOfBatches}` || '-'}
                          </Table.Td>
                        )}
                        <Table.Td
                          onClick={() => {
                            setOpenSideBar(true);
                            setSideBarData(num?._id);
                          }}
                        >
                          {unCamelCaseString(num?.status) || 'NA'}
                        </Table.Td>

                        {!isMobile && (
                          <Table.Td>
                            <Button
                              onClick={() => {
                                setQrData(num);
                                setSendQrData(num);
                                setIsOpenQr(true);
                              }}
                            >
                              View
                            </Button>
                          </Table.Td>
                        )}

                        {!isMobile && (
                          <Table.Td>
                            <Button
                              onClick={() => {
                                setQrData(num);
                                setIsOpenFor('printJob');
                              }}
                            >
                              Print
                            </Button>
                          </Table.Td>
                        )}

                        {status === 'ongoing' ? (
                          <Table.Options
                            className={'bg-white'}
                            onEdit={() => navigate(`edit/${num._id}`)}
                            onCopy={() => handleCopyClick(num)}
                            onSend-Progress-Report={() => {
                              getDetails(num?._id);
                            }}
                            onAddToPriority={() =>
                              addToPriorityQueue(num?._id, num?.id)
                            }
                            onQcData={() => {
                              navigate(`/jobs/qc?job=${num?._id}`);
                            }}
                          />
                        ) : (
                          <Table.Options
                            className={'bg-white'}
                            onEdit={() => navigate(`edit/${num._id}`)}
                            onCopy={() => handleCopyClick(num)}
                            onSend-Progress-Report={() => {
                              getDetails(num?._id);
                            }}
                            onAddToPriority={() =>
                              addToPriorityQueue(num?._id, num?.id)
                            }
                            onDelete={() => {
                              setShowPasswordModal((prev) => {
                                return !prev;
                              });
                              setJobSelectedForDelete(num);
                            }}
                            onQcData={() => {
                              navigate(`/jobs/qc?job=${num?._id}`);
                            }}
                          />
                        )}
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            </div>
            <Pagination
              limit={limit}
              page={page}
              totalPages={totalPages}
              totalResults={totalResults}
              setPage={(e) =>
                setSearchParams(
                  (prev) => {
                    prev.set('page', e);
                    return prev;
                  },
                  { replace: true }
                )
              }
              setLimit={(e) =>
                setSearchParams(
                  (prev) => {
                    prev.set('limit', e);
                    return prev;
                  },
                  { replace: true }
                )
              }
            />
          </>
        )}
      </div>
    </>
  );
};

export default ManageJobs;
