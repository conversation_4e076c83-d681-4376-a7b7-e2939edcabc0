import React, { useContext, useEffect, useState } from 'react';
// import { toast } from 'react-toastify';
// import { ReactComponent as Doc } from '../../../assets/svgs/documentprevious.svg';
import { generateGoalsData, generateGoalsTable } from '../../../helperFunction';
import usePrefixIds from '../../../hooks/usePrefixIds';
import { useGetAllMachinesQuery } from '../../../slices/machineApiSlice';
import { useGetAllProcessGoalsQuery } from '../../../slices/processGoalApiSlice';
import { useGetAllProductionFLowsQuery } from '../../../slices/productionFlowApiSlice';
import { Store } from '../../../store/Store';
import { customConfirm } from '../../../utils/customConfirm';
import Input from '../../global/components/Input';
// import Modal from '../../global/components/Modal';
import Toggle from '../../global/components/Toggle';
import { Modal as AntModal } from 'antd';

export default function AddJobDetailsModal({
  workOrderId,
  setAddJobDetails,
  addJobDetails,
  // allJobData,
  // setAllJobData,
  orderQuantity,
  deadline,
  submitHandler = undefined,
}) {
  const [allInputs, setAllInputs] = useState({
    deadline: deadline || '',
    isSingleBatch: true,
    isSequential: false,
    goalsTables: [],
    goalsData: {},
  });

  const [singleGoalsData, setSingleGoalsData] = useState({});

  const [goalsKeys, setGoalsKeys] = useState([]);
  const { defaults } = useContext(Store);

  const fetchedTemplate = addJobDetails?.jobTemplate || null;
  const { data: productionFlowRes = {} } = useGetAllProductionFLowsQuery();
  const { data: processGoalRes = {} } = useGetAllProcessGoalsQuery();
  const { data: allMachinesRes = {} } = useGetAllMachinesQuery();
  const { machines: allMachines = [] } = allMachinesRes;
  const { processGoals = [] } = processGoalRes;
  const { productionFlows = [] } = productionFlowRes;
  const productionFlow = productionFlows?.find(
    (pf) => pf._id === fetchedTemplate?.flow?._id
  );

  const jobPrefix = usePrefixIds({
    idFor: 'jobId',
    additionalIdData: { WorkOrderId: workOrderId },
  });
  const modelPrefix = usePrefixIds({
    idFor: 'modelName',
    additionalIdData: { WorkOrderId: workOrderId },
  });
  const additionalIdData = {
    WorkOrderId: workOrderId,
    JobId: jobPrefix?.currentPrefixId,
    ModelName: modelPrefix?.currentPrefixId,
    InputScreen: fetchedTemplate?.inputScreen?.name,
  };
  const batchPrefix = usePrefixIds({
    idFor: 'batchId',
    additionalIdData: additionalIdData,
  });

  const handleModalSubmit = async () => {
    const tempMachinesWithFlowId = {};
    const productionFlow = productionFlows?.find(
      (pf) => pf._id === fetchedTemplate?.flow?._id
    );
    if (allMachines?.length > 0) {
      productionFlow.processes.forEach((el) => {
        tempMachinesWithFlowId[el?._id] = tempMachinesWithFlowId[el?._id] || [];

        const machines = allMachines.filter((mElem) => {
          return el?.mqtt?._id === mElem.mqtt;
        });

        let tempMachines = [];
        machines.forEach((mac) => {
          const itemsPerHour =
            mac?.itemsPerHour.find((iPH) => iPH.isDefault)?.value || 0;
          const changeOverTime =
            mac?.changeOverTime.find((cOT) => cOT.isDefault)?.value || 0;

          tempMachines = [
            ...tempMachines,
            {
              label: mac?.machineName,
              value: mac?._id,
              itemsPerHour,
              changeOverTime,
              capacity: mac.maxCapacity,
              costPerHour: mac.costPerHour,
            },
          ];
        });

        tempMachinesWithFlowId[el?._id] = [
          ...tempMachinesWithFlowId[el?._id],
          ...tempMachines,
        ];
      });
    }

    setAllInputs((prev) => {
      const tempGoalsTables = prev.goalsTables.map((el) => {
        let compatibleMachines = Object.keys(tempMachinesWithFlowId).find(
          (currElem) => currElem === el.flowId
        );

        compatibleMachines = tempMachinesWithFlowId[compatibleMachines];

        return { ...el, compatibleMachines };
      });

      return { ...prev, goalsTables: tempGoalsTables };
    });

    const allGoalsTable = allInputs.goalsTables.map((el) => {
      let compatibleMachines = Object.keys(tempMachinesWithFlowId).find(
        (currElem) => currElem === el.flowId
      );

      let applicableDowntimes = Object.keys(
        fetchedTemplate?.applicableDowntimes || {}
      ).find((currElem) => currElem === el.flowId);

      if (compatibleMachines && applicableDowntimes) {
        compatibleMachines = tempMachinesWithFlowId[compatibleMachines];
        applicableDowntimes =
          fetchedTemplate?.applicableDowntimes[applicableDowntimes];

        return { ...el, compatibleMachines, applicableDowntimes };
      }
      return el;
    });

    // const alreadyExists = allJobData.find(
    //   (job) => job.modelName === modelPrefix?.currentPrefixId
    // );

    // if (alreadyExists) {
    //   toast.error('Job already added with same modelName');
    //   return;
    // }

    if (fetchedTemplate) {
      const imageURLs = {
        ...fetchedTemplate?.attachments,
        project: [
          ...(fetchedTemplate?.attachments?.project ?? []),
          ...(addJobDetails?.attachments ?? []),
        ],
      };

      let data = {
        goalsTable: allGoalsTable,
        goalsData: allInputs.goalsData,
        jobPlanningDetails: {
          isSingleBatch: allInputs.isSingleBatch,
          isSequential: allInputs.isSequential,
          deadline: allInputs.deadline,
        },
        allData: fetchedTemplate?.allData ?? [],
        id: jobPrefix?.currentPrefixId,
        name: fetchedTemplate?.inputScreen?.name,
        inputScreen: fetchedTemplate?.inputScreen?.id,
        imageURLs,
        modelName: modelPrefix?.currentPrefixId,
        projectNo: 1,
        parentItemId: addJobDetails?.parentItemId,
        isForBom: addJobDetails?.isForBom,
        multiProcessData: fetchedTemplate?.multiProcessData ?? {},
        inputScreenTableData: fetchedTemplate?.inputScreenTableData ?? {},
        linkedForms: fetchedTemplate?.linkedForms ?? [],
        productionFlow: fetchedTemplate?.flow?._id,
        linkedAssemblyForms: fetchedTemplate?.linkedAssemblyForms ?? [],
        assemblyItemToUpdate: addJobDetails.assemblyItemToUpdate || null,
        assemblyPart: addJobDetails.assemblyPart || null,
        assemblyProduct: addJobDetails.assemblyProduct || null,
        assemblyManualEntry: addJobDetails.assemblyManualEntry || null,
        _uniqueId: addJobDetails?.id,
        mqttId: productionFlow?.processes[0].mqtt?._id,
        idsData: {
          jobIdData: { idData: jobPrefix?.idCompData?.dataToReturn },
          modelIdData: { idData: modelPrefix?.idCompData?.dataToReturn },
          batchIdData: { idData: batchPrefix?.idCompData?.dataToReturn },
          additionalIdData,
        },
        jobCompletionQcForm: [],
      };

      if (submitHandler !== undefined) {
        await submitHandler(data);
      }
    }
  };

  useEffect(
    () => {
      if (fetchedTemplate?.flow?._id) {
        let tempGoalsData = {};
        productionFlow?.processes?.forEach((pro) => {
          const goal = processGoals?.find(
            (goal) => goal?.mqtt?._id === pro?.mqtt?._id
          );

          let tempOrderQuantity = orderQuantity
            ? addJobDetails?.units *
                (addJobDetails?.requiredQuantity
                  ? addJobDetails?.requiredQuantity
                  : orderQuantity) -
                addJobDetails?.inStock <=
              0
              ? 0
              : addJobDetails?.units *
                  (addJobDetails?.requiredQuantity
                    ? addJobDetails?.requiredQuantity
                    : orderQuantity) -
                +addJobDetails?.inStock
            : 0;

          goal?.parameters?.forEach((param) => {
            tempGoalsData = {
              ...tempGoalsData,
              [pro._id]: {
                ...(tempGoalsData[pro._id] || {}),
                [param.name]:
                  param?.name === 'Number of Ups' ||
                  param?.name === 'Number of Batches'
                    ? 1
                    : param.name === 'Order Quantity' ||
                        param.name === 'Batch Size' ||
                        param.name === 'Max Size of Batch'
                      ? tempOrderQuantity || 0
                      : 0,
              },
            };
          });
        });

        const tempGoalsTables = generateGoalsTable(
          tempGoalsData,
          productionFlow,
          defaults.defaultParam,
          {
            multiProcessData:
              fetchedTemplate?.multiProcessTablesData?.multiProcessData || {},
            inputScreenTableData:
              fetchedTemplate?.inputScreenTableData?.multiProcessData || {},
          },
          [],
          fetchedTemplate?.multiplier
        );

        setAllInputs((prev) => ({
          ...prev,
          goalsTables: tempGoalsTables,
          goalsData: tempGoalsData,
        }));
        let tempSingleGoalsData =
          tempGoalsData?.[Object.keys(tempGoalsData || {})?.[0]];
        setSingleGoalsData(() => tempSingleGoalsData);
        let gKey = Object.keys(tempSingleGoalsData || {})?.filter(
          (i) => i !== 'Speed' && i !== 'Time'
        );
        setGoalsKeys(() => gKey);
      }
    },
    // eslint-disable-next-line
    [
      fetchedTemplate,
      addJobDetails?.bomItemId,
      addJobDetails?.units,
      addJobDetails?.isOpen,
      processGoals,
      productionFlows,
    ]
  );

  useEffect(() => {
    if (allInputs.isSingleBatch) {
      setAllInputs((prev) => ({ ...prev, noOfBatches: 1 }));
    }
  }, [allInputs.isSingleBatch]);

  const handleGoalsDataChange = (e) => {
    const { name, value } = e.target;
    let tempGoalsData = allInputs.goalsData;

    if (name === 'Batch Size' && singleGoalsData?.['Order Quantity'] < +value) {
      return;
    }

    let tempOrderQuantity = orderQuantity
      ? addJobDetails?.units * orderQuantity - addJobDetails?.inStock <= 0
        ? 0
        : addJobDetails?.units * orderQuantity - addJobDetails?.inStock
      : 0;

    tempOrderQuantity =
      +value <= tempOrderQuantity ? +value : tempOrderQuantity;

    if (
      name === 'Order Quantity' &&
      singleGoalsData?.['Batch Size'] > +tempOrderQuantity
    ) {
      return;
    }
    if (name === 'Order Quantity') {
      Object.keys(tempGoalsData).forEach((el) => {
        tempGoalsData[el] = {
          ...tempGoalsData[el],
          'Order Quantity': value,
        };
      });
    }

    const newTempGoalsData = generateGoalsData(
      tempGoalsData,
      name,
      name === 'Order Quantity' ? tempOrderQuantity : value,
      allInputs.isSingleBatch
    );

    const tempGoalsTables = generateGoalsTable(
      newTempGoalsData,
      productionFlow,
      defaults.defaultParam,
      {
        multiProcessData:
          fetchedTemplate?.multiProcessTablesData?.multiProcessData || {},
        inputScreenTableData:
          fetchedTemplate?.inputScreenTableData?.multiProcessData || {},
      },
      []
    );

    setAllInputs((prev) => ({
      ...prev,
      goalsTables: tempGoalsTables,
      goalsData: newTempGoalsData,
    }));

    setSingleGoalsData(
      () => newTempGoalsData?.[Object.keys(newTempGoalsData)[0]]
    );
  };

  const handleToggleChange = () => {
    if (!allInputs.isSingleBatch) {
      let tempOrderQuantity = orderQuantity
        ? addJobDetails?.units * orderQuantity - addJobDetails?.inStock <= 0
          ? 0
          : addJobDetails?.units * orderQuantity - addJobDetails?.inStock
        : 0;

      const tempGoalsData = generateGoalsData(
        allInputs.goalsData,
        'Order Quantity',
        tempOrderQuantity || 0,
        true
      );

      const tempGoalsTables = generateGoalsTable(
        tempGoalsData,
        productionFlow,
        defaults.defaultParam,
        {
          multiProcessData:
            fetchedTemplate?.multiProcessTablesData?.multiProcessData || {},
          inputScreenTableData:
            fetchedTemplate?.multiProcessTablesData?.multiProcessData || {},
        },
        []
      );
      setAllInputs((prev) => ({
        ...prev,
        goalsTables: tempGoalsTables,
        goalsData: tempGoalsData,
      }));
      setSingleGoalsData(() => tempGoalsData?.[Object.keys(tempGoalsData)[0]]);
    }
    setAllInputs((prev) => ({
      ...prev,
      isSingleBatch: !prev.isSingleBatch,
    }));
  };

  return (
    <AntModal
      title="Add Job Details"
      description="You've to add job details to be able to create a job for the product"
      width={1000}
      open={addJobDetails.isOpen}
      onOk={handleModalSubmit}
      onCancel={async () => {
        if (
          !(await customConfirm(
            'Are you sure you want to close this modal?',
            'delete'
          ))
        ) {
          return;
        }
        setAddJobDetails({
          isOpen: false,
          template: null,
          id: null,
        });
      }}
    >
      <div className="w-full">
        <div className="w-full">
          <h4 className="mb-4">Order Details</h4>
          <div className="grid grid-cols-2 gap-4 w-full">
            <div className="w-full">
              <label className="text-black font-medium text-sm block mb-2">
                Single Batch
              </label>
              <Toggle
                value={allInputs.isSingleBatch}
                onChange={handleToggleChange}
              />
            </div>
            <div className="w-full">
              <label className="text-black font-medium text-sm block mb-2">
                Sequential
              </label>
              <Toggle
                value={allInputs.isSequential}
                onChange={() =>
                  setAllInputs((prev) => ({
                    ...prev,
                    isSequential: !prev.isSequential,
                  }))
                }
              />
            </div>

            {goalsKeys.map((gKey, gIdx) => {
              const isFormula = gKey === 'Number of Batches';
              const isHidden =
                gKey === 'Max Size of Batch' ||
                (gKey === 'Batch Size' && allInputs.isSingleBatch);

              return (
                !isHidden && (
                  <div className="w-full" key={gIdx}>
                    <label className="text-black font-medium text-sm block mb-2">
                      {gKey}
                    </label>
                    <Input
                      name={gKey}
                      onChange={(e) => handleGoalsDataChange(e)}
                      readOnly={isFormula}
                      value={singleGoalsData[gKey]}
                    />
                  </div>
                )
              );
            })}
          </div>
        </div>
        <div className="mt-4 space-y-4 ">
          <h4 className="mb-4">Job Details</h4>
          <div className="w-full flex-col">
            {workOrderId && (
              <>
                <div className="flex w-full items-center gap-4 mb-2">
                  <label className="text-sm text-black font-normal flex gap-2">
                    <div className="text-sm text-black font-semibold">
                      {defaults?.defaultParam?.projectDefaults?.modelLabel}
                    </div>
                    :
                  </label>
                  <modelPrefix.IdGenComp {...modelPrefix?.idCompData} />
                </div>
                <div className="flex gap-4 items-center mb-2">
                  <label className="text-md text-black font-semibold">
                    {defaults?.defaultParam?.projectDefaults?.projectIdentifier}
                    :
                  </label>
                  <jobPrefix.IdGenComp {...jobPrefix?.idCompData} />
                </div>
                <div className="flex w-full items-center gap-4 mb-2">
                  <label className="text-sm text-black font-normal flex gap-2">
                    <div className="text-sm text-black font-semibold">
                      Batch ID Format{' '}
                    </div>
                    :
                  </label>
                  <batchPrefix.IdGenComp {...batchPrefix?.idCompData} />
                </div>
                <div className="flex">
                  <p className="text-md text-black font-semibold">
                    Production Flow:{' '}
                  </p>
                  {fetchedTemplate?.flow?.processes?.map(
                    (process, index, array) => (
                      <React.Fragment key={process._id}>
                        &nbsp;
                        <span className="text-md">{process.processName}</span>
                        {index !== array.length - 1 && (
                          <>
                            &nbsp;
                            {' > '}
                          </>
                        )}
                      </React.Fragment>
                    )
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </AntModal>
  );
}
