import { Modal, Radio, Table } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useGetAllAssetsForOptionsQuery } from '../../../slices/assetApiSlice';
import { customConfirm } from '../../../utils/customConfirm';
import Input from '../../global/components/Input';
import MultiSelect from '../../global/components/MultiSelect';
import PartModalV2 from '../InventoryMasters/PartModalV2';

const RMModal = ({
  openModal,
  setOpenModal,
  record,
  recordIndex,
  setBomItems,
  allParts,
  orderQuantity,
}) => {
  const [mode, setMode] = useState('rawMaterials');
  const [selectedRawMaterials, setSelectedRawMaterials] = useState([]);
  const [rawMaterials, setRawMaterials] = useState([]);
  const [selectedAssets, setSelectedAssets] = useState([]);
  const [isOpenAddPart, setIsOpenAddPart] = useState(false);

  const allRawMaterialParts = useMemo(() => {
    if (allParts && record) {
      let temp = allParts
        ?.filter((el) => el?.category?.name?.toLowerCase() === 'raw materials')
        ?.map((el) => ({ label: el?.name, value: el?.value, ...el }));
      let bomRawMaterialIds = record?.rawMaterials?.map(
        (elem) => elem?.item?._id || elem?.value
      );
      temp = temp?.filter((elem) => !bomRawMaterialIds?.includes(elem?.value));
      let bomRawMaterials = record?.rawMaterials?.map((elem) => {
        return {
          ...elem,
          ...elem?.item,
        };
      });
      return [...(bomRawMaterials || []), ...(temp || [])];
    }
  }, [allParts, record]);

  const { data: allAssets = [] } = useGetAllAssetsForOptionsQuery(
    {},
    { refetchOnMountOrArgChange: true }
  );

  const handleModeChange = (e) => {
    setMode(e.target.value);
  };

  useEffect(() => {
    if (record?.rawMaterials?.length > 0 && allRawMaterialParts !== undefined) {
      setSelectedRawMaterials(
        record?.rawMaterials?.map((elem) => elem?.item?._id || elem?.value)
      );
    }
  }, [record, allRawMaterialParts]);

  useEffect(() => {
    if (record?.assetData?.length > 0 && allAssets !== undefined) {
      setSelectedAssets(
        record?.assetData?.map((elem) => elem?.value || elem?.value)
      );
    }
  }, [record, allAssets]);

  useEffect(() => {
    if (allRawMaterialParts && selectedRawMaterials) {
      let temp = allRawMaterialParts?.map((elem) => {
        if (selectedRawMaterials?.includes(elem?.value || elem?._id))
          return elem;
      });
      setRawMaterials(temp?.filter((elem) => elem !== undefined));
    }
  }, [selectedRawMaterials, allRawMaterialParts]);

  const subtractReserved = (data) => {
    if (!data?.quantity) return 0;
    const diff = data?.quantity - (data?.reserved || 0);
    return (diff <= 0 ? 0 : diff) || 0;
  };

  const getInStockInteger = (item) => {
    const diff = item?.quantity - (item?.reserved || 0);
    return (diff <= 0 ? 0 : diff) || 0;
  };

  const getInStock = (item) => {
    return `${subtractReserved(item) || 0}\xA0(${
      item?.quantity?.toFixed(2) || 0
    },\xA0${item?.reserved?.toFixed(2) || 0})`;
  };

  const rawMaterialColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => {
        return <span>{record?.name}</span>;
      },
    },
    {
      title: 'Units',
      dataIndex: 'units',
      key: 'units',
      render: (_, record, index) => (
        <Input
          value={record?.units}
          onChange={(e) => {
            setRawMaterials((prev) =>
              prev?.map((elem, elemIndex) => {
                if (index === elemIndex) {
                  return {
                    ...elem,
                    units: +e.target.value,
                  };
                } else {
                  return elem;
                }
              })
            );
          }}
        />
      ),
    },
    {
      title: 'In Stock',
      dataIndex: 'inStock',
      key: 'inStock',
      render: (_, record) => <span>{getInStock(record)}</span>,
    },
    {
      title: 'Required Stock',
      dataIndex: 'requiredStock',
      key: 'requiredStock',
      render: (_, row) => {
        return (
          <span>
            {orderQuantity * (row?.units || 0) - getInStockInteger(row) < 0
              ? 0
              : orderQuantity * (row?.units || 0) - getInStockInteger(row)}
          </span>
        );
      },
    },
  ];

  function cleanText(htmlString) {
    // Decode HTML entities (if any)
    let text = htmlString?.replace(/&lt;/g, '<').replace(/&gt;/g, '>');

    // Remove all HTML tags
    text = text?.replace(/<[^>]*>/g, '');

    // Replace multiple spaces with a single space
    text = text?.replace(/\s+/g, ' ').trim();

    return text;
  }

  const assetColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => {
        return <span>{record?.name}</span>;
      },
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (_, record) => {
        return <span>{cleanText(record?.description)}</span>;
      },
    },
  ];

  const setRawMateriasInBomItems = () => {
    setBomItems((prev) =>
      prev?.map((elem, index) => {
        if (index === recordIndex) {
          return {
            ...prev?.[recordIndex],
            rawMaterials: rawMaterials
              ?.filter((elem) => elem?.units <= 0 || elem?.units !== undefined)
              ?.map((item) => ({
                label: item?.label,
                name: item?.name,
                value: item?.value,
                units: item?.units,
                type: item?.type,
              })),
          };
        } else {
          return elem;
        }
      })
    );
    setSelectedRawMaterials([]);
    setRawMaterials([]);
  };

  const setAssetsInBomItems = () => {
    let assetData = allAssets?.filter((elem) =>
      selectedAssets?.includes(elem?.value)
    );
    setBomItems((prev) =>
      prev?.map((elem, index) => {
        if (index === recordIndex) {
          return {
            ...prev?.[recordIndex],
            assetData,
          };
        } else {
          return elem;
        }
      })
    );
    setSelectedAssets([]);
  };

  return (
    <>
      {mode === 'rawMaterials' && (
        <PartModalV2
          openModal={isOpenAddPart}
          setOpenModal={setIsOpenAddPart}
        />
      )}
      <Modal
        title="Select RM"
        centered
        open={openModal}
        onOk={() => {
          setOpenModal({ open: false, record: {}, recordIndex: -1 });
          setRawMateriasInBomItems();
          setAssetsInBomItems();
        }}
        onCancel={async () => {
          const confirm = await customConfirm(
            `Are you sure you want to cancel setting Raw Materials? All changes will be lost.`,
            'stop'
          );
          if (!confirm) return;
          setOpenModal({ open: false, record: {}, recordIndex: -1 });
          setSelectedRawMaterials([]);
          setRawMaterials([]);
        }}
        width="60%"
        styles={{
          body: {
            height: '20em',
            overflowY: 'auto',
          },
        }}
      >
        <div>
          <Radio.Group
            onChange={handleModeChange}
            value={mode}
            style={{
              marginBottom: 8,
            }}
          >
            <Radio.Button value="rawMaterials">Raw Materials</Radio.Button>
            <Radio.Button value="assets">Assets</Radio.Button>
          </Radio.Group>
          {mode === 'rawMaterials' && (
            <div>
              <div>
                <h4>Select Raw Materials</h4>
                <MultiSelect
                  AddOption="+ Add Part"
                  handleAddFunction={() => setIsOpenAddPart(true)}
                  options={[
                    ...allRawMaterialParts?.map((elem) => ({
                      label: elem?.name,
                      value: elem?.value,
                    })),
                  ]}
                  value={selectedRawMaterials}
                  onChange={(e) => {
                    setSelectedRawMaterials(
                      e.target.value
                        ? e.target.value
                            ?.map((elem) => elem?.value)
                            ?.filter((elem) => elem !== 'addPart')
                        : []
                    );
                  }}
                />
              </div>
              <div>
                <Table
                  columns={rawMaterialColumns}
                  dataSource={rawMaterials || []}
                  rowKey="_id"
                  pagination={false}
                  scroll={{ x: 'max-content' }}
                  className="shadow-md rounded-lg"
                />
              </div>
            </div>
          )}
          {mode === 'assets' && (
            <div>
              <div>
                <h4>Select Assets</h4>
                <MultiSelect
                  options={allAssets?.map((el) => ({
                    ...el,
                    label: el?.name,
                  }))}
                  value={selectedAssets}
                  onChange={(e) =>
                    setSelectedAssets(
                      e.target.value?.map((elem) => elem?.value)
                    )
                  }
                />
              </div>
              <div>
                <Table
                  columns={assetColumns}
                  dataSource={allAssets?.filter((elem) =>
                    selectedAssets?.includes(elem?.value)
                  )}
                  rowKey="_id"
                  pagination={false}
                  scroll={{ x: 'max-content' }}
                  className="shadow-md rounded-lg"
                />
              </div>
            </div>
          )}
        </div>
      </Modal>
    </>
  );
};

export default RMModal;
