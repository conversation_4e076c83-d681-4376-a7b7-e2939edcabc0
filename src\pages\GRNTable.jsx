import {
  DownloadOutlined,
  ExportOutlined,
  FileExcelOutlined,
  PlusCircleOutlined,
  PrinterOutlined,
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Divider,
  Dropdown,
  Input,
  Modal,
  Table,
  Typography,
} from 'antd';
import { useContext, useEffect, useState } from 'react';
import QRCode from 'react-qr-code';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';
import * as XLSX from 'xlsx';
import GRNSideBar from '../components/CreateGRN/GRNSideBar';
import PendingStockIn from '../components/CreateGRN/PendingStockIn.jsx';
import TaskTable from '../components/TaskContainer/TaskTable.jsx';
import {
  FilterIcon,
  FilterV2,
} from '../components/global/components/FilterV2.jsx';
import Header from '../components/global/components/Header.jsx';
import { InfoTooltip } from '../components/global/components/InfoTooltip.jsx';
import Pagination from '../components/global/components/Pagination';
import { getLocalDate } from '../helperFunction';
import useDebounceValue from '../hooks/useDebounceValue';
import {
  useGetFilterOptionsQuery,
  useLazyGetAllGrnsForExportQuery,
  useLazyGetPaginatedGrnsQuery,
} from '../slices/inPageApiSlice';
import { Store } from '../store/Store.js';

const { Text, Title } = Typography;

const ItemBadge = ({
  label,
  value,
  color,
  width = 'w-44',
  showTooltip = true,
  tooltipPrefix = '',
}) => {
  const displayValue =
    value.length > 6 && width === 'w-24'
      ? value.substring(0, 6) + '...'
      : value;
  const tooltipText = tooltipPrefix ? `${tooltipPrefix}: ${value}` : value;

  return (
    <span
      className={`${color} px-2 py-0.5 text-xs rounded ${width} text-center truncate font-semibold flex items-center justify-center gap-1`}
      data-tooltip-id="item-tooltip"
      data-tooltip-content={showTooltip ? tooltipText : null}
    >
      <span className="lg:hidden">{label}:</span>
      <span>{displayValue}</span>
    </span>
  );
};

const QrModal = ({ open, onClose, inpages }) => {
  const [selected, setSelected] = useState(null);

  useEffect(() => {
    if (inpages?.length > 0) {
      setSelected(inpages[0]);
    }
  }, [inpages]);

  const handlePrint = () => {
    setTimeout(() => {
      window.print();
    }, 500);
  };

  const batchNumber = selected?.batchNo || '-';
  const lotNumber = selected?.lotNo || '-';
  const quantity = selected?.quantity || 0;

  let line = `Batch No: ${batchNumber}\nLot No: ${lotNumber}\nReceived Qty: ${quantity}\nDate: `;

  if (selected?.part) {
    line += `\nPart: ${selected?.part?.name}\nUom: ${selected?.selectedUOM?.uom || '-'}`;
  } else if (selected?.product) {
    line += `\nProduct: ${selected?.product?.name}\nUom: ${selected?.selectedUOM?.uom || '-'}`;
  }

  const qrValue = line;

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      width={1200}
      title="QR Code"
    >
      <div className="flex flex-col md:flex-row items-start justify-between gap-6 p-4">
        <div className="bg-white p-4 rounded-lg shadow w-60 text-center">
          <QRCode
            id="print-qr"
            className="mb-4"
            value={qrValue}
            size={200}
            bordered={false}
          />
          <Text className="text-sm font-semibold text-gray-500">
            {selected?.part?.name || selected?.product?.name}
          </Text>
          <div className="flex justify-center mt-4">
            <Button
              type="primary"
              icon={<PrinterOutlined />}
              onClick={handlePrint}
            >
              Print
            </Button>
          </div>
        </div>
        <div className="flex-1 h-[400px] overflow-y-auto pr-2">
          <Title level={5} className="mb-2">
            Item Details
          </Title>
          <Text className="block mb-4 text-gray-500">
            Select an item below to generate its QR code.
          </Text>
          <Divider className="my-2" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {inpages?.map((item) => (
              <div
                key={item._id}
                className={`h-36 p-3 border rounded-lg overflow-y-auto cursor-pointer transition-all ${
                  selected?._id === item._id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200'
                }`}
                onClick={() => setSelected(item)}
              >
                <div className="flex justify-between items-center mb-2">
                  <Text strong>{item.part?.name || item.product?.name}</Text>
                </div>
                <div className="grid grid-cols-1 gap-y-2">
                  <div>
                    <Text type="secondary">Batch No:</Text>
                    <Text className="ml-2">
                      {item.batchNo} ({item?.lotNo})
                    </Text>
                  </div>
                  <div className="col-span-2">
                    <Text type="secondary">Quantity:</Text>
                    <Text className="ml-2">{item?.quantity}</Text>
                  </div>
                  <div className="col-span-2">
                    <Text type="secondary">UOM:</Text>
                    <Text className="ml-2">{item?.selectedUOM?.uom}</Text>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Modal>
  );
};

const GRNTable = () => {
  const [getPaginatedGrns, { data: grns }] = useLazyGetPaginatedGrnsQuery();
  const [getAllGRNsForExport, { data: allGrns, isLoading: isExportLoading }] =
    useLazyGetAllGrnsForExportQuery();
  const { defaults } = useContext(Store);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(25);
  const [searchTerm, setSearchTerm] = useState('');
  const [openSideBar, setOpenSideBar] = useState(false);
  const [grnDetails, setGrnDetails] = useState({});
  const [loading, setLoading] = useState(false);
  const [openQRModal, setOpenQRModal] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const currentTab = searchParams.get('tab') || 'stockin';
  const navigate = useNavigate();
  const debounceSearch = useDebounceValue(searchTerm || '');
  const [filters, setFilters] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const { data: filterData } = useGetFilterOptionsQuery();
  const filterOptions = filterData?.filterOptions || {};

  const filterConfig = [
    {
      key: 'id',
      label: 'GRN ID',
      path: 'id',
      type: 'multiSelect',
      options: filterOptions?.grnIds || [],
    },
    {
      key: 'batchNo',
      label: 'Batch No',
      path: 'filterObject.batchNo',
      type: 'multiSelect',
      options: filterOptions?.batchNos || [],
    },
    {
      key: 'date',
      label: 'Date',
      path: 'createdAt',
      type: 'date',
    },
    {
      key: 'part',
      label: 'Part Name',
      path: 'filterObject.part',
      type: 'multiSelect',
      options: filterOptions?.partNames || [],
      isObjectId: true,
    },
    {
      key: 'product',
      label: 'Product Name',
      path: 'filterObject.product',
      type: 'multiSelect',
      options: filterOptions?.productNames || [],
      isObjectId: true,
    },
    {
      key: 'store',
      label: 'Store',
      path: 'store',
      type: 'multiSelect',
      options: filterOptions?.stores || [],
      isObjectId: true,
    },
    {
      key: 'selectedUOM',
      label: 'UOM',
      path: 'filterObject.uom',
      type: 'multiSelect',
      options: filterOptions?.selectedUOMs || [],
    },
    // {
    //   key: 'Quantity',
    //   label: 'Quantity',
    //   path: 'inpages.quantity',
    //   type: 'multiSelect',
    //   options: filterOptions?.quantities || [],
    // },
  ];

  useEffect(() => {
    if (currentTab === 'stockin') {
      const fetchGrns = async () => {
        setLoading(true);
        try {
          await getPaginatedGrns({
            page,
            limit,
            searchTerm: debounceSearch,
            filters,
          }).unwrap();
        } catch (err) {
          toast.error('Failed to fetch GRNs');
        } finally {
          setLoading(false);
        }
      };
      fetchGrns();
    }
  }, [page, limit, debounceSearch, currentTab, filters]); // eslint-disable-line

  const columns = [
    {
      title: 'GRN ID',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
      render: (text) => (
        <Text strong type="primary" className="text-xs">
          {text}
        </Text>
      ),
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      align: 'center',
      render: (text) => (
        <span
          data-tooltip-id="date-tooltip"
          data-tooltip-content={new Date(text).toLocaleString()}
        >
          <Text className="text-xs">{getLocalDate(text)}</Text>
        </span>
      ),
    },
    {
      title: 'Store',
      dataIndex: 'store',
      key: 'store',
      width: 140,
      align: 'center',
      render: (store) => (
        <div className="text-center">
          <Text className="text-xs">{store?.name}</Text>
        </div>
      ),
    },
    {
      title: 'QR',
      dataIndex: 'qr',
      key: 'qr',
      align: 'center',
      render: (_, record) => (
        <div
          onClick={(e) => {
            e.stopPropagation();
            setOpenQRModal(true);
            setGrnDetails(record);
          }}
          className="flex justify-center"
        >
          <QRCode value={record?._id} size={20} />
        </div>
      ),
    },
    {
      title: (
        <div className="flex items-center justify-between">
          <span>Items</span>
          <div className="hidden gap-x-4 justify-end lg:flex">
            <span className="w-24 text-center">Quantity</span>
            <span className="w-24 text-center">UOM</span>
            <span className="w-24 text-center">Scrap</span>
          </div>
        </div>
      ),
      key: 'items',
      render: (_, record) => (
        <div className="py-1 w-full">
          {record?.inpages?.map((item, index) => {
            const itemName = item?.part?.name || item?.product?.name || '-';
            const batch = item?.batchNo || '-';
            const lot = item?.lotNo || '-';
            const qty = item?.quantity || '0';
            const uom = item?.selectedUOM?.uom || '-';
            const scrap = item?.scrapQuantity;

            return (
              <div
                key={`${batch}-${lot}-${index}`}
                className="p-2 mb-1 bg-gray-50 rounded border border-gray-100 last:mb-0"
              >
                <div className="flex justify-between items-center">
                  <span
                    className="font-semibold text-xs truncate max-w-xs mr-2"
                    data-tooltip-id="item-tooltip"
                    data-tooltip-content={itemName}
                  >
                    {itemName}
                  </span>

                  <div className="flex flex-wrap gap-2 justify-end">
                    <ItemBadge
                      label="Qty"
                      value={qty}
                      color="bg-blue-100 text-blue-800"
                      tooltipPrefix="Quantity"
                      width="w-24"
                    />
                    <ItemBadge
                      label="UOM"
                      value={uom}
                      color="bg-purple-100 text-purple-800"
                      tooltipPrefix="UOM"
                      width="w-24"
                    />
                    <ItemBadge
                      label="Scrap"
                      value={scrap?.toString() || 'N/A'}
                      color="bg-orange-100 text-orange-800"
                      tooltipPrefix="Scrap Quantity"
                      width="w-24"
                    />
                  </div>
                </div>
              </div>
            );
          })}
          {!record?.inpages?.length && (
            <span className="italic text-gray-400 text-xs">No items</span>
          )}
        </div>
      ),
    },
  ];

  const exportToExcel = (data) => {
    if (!data || !data.length) return;

    const excelData = [];
    const merges = [];
    let rowIndex = 1;
    let grnSrNo = 1;

    data.forEach((grn) => {
      const startRow = rowIndex;

      if (!grn.inpages || grn.inpages.length === 0) {
        excelData.push({
          'Sr No.': grnSrNo,
          'GRN ID': grn.id,
          Date: new Date(grn.createdAt).toLocaleDateString(),
          Items: '-',
          Batch: '-',
          Quantity: '-',
          UOM: '-',
          'Scrap Qty': '-',
        });
        grnSrNo++;
        rowIndex++;
        return;
      }

      grn.inpages.forEach((item, idx) => {
        const itemName = item?.part?.name || item?.product?.name || '-';
        const batch = `${item?.batchNo ?? ''} (${item?.lotNo ?? ''})` || '-';
        const qty = item?.quantity || '0';
        const uom = item?.selectedUOM?.uom || '-';
        const scrap =
          item?.scrapQuantity > 0 ? item.scrapQuantity.toString() : '-';

        excelData.push({
          'Sr No.': idx === 0 ? grnSrNo : '', // Show Sr No. only on first row of each GRN
          'GRN ID': grn.id,
          Date: new Date(grn.createdAt).toLocaleDateString(),
          Items: itemName,
          Batch: batch,
          Quantity: qty,
          UOM: uom,
          'Scrap Qty': scrap,
        });
        rowIndex++;
      });

      const endRow = rowIndex - 1;

      if (endRow > startRow) {
        merges.push({ s: { r: startRow, c: 0 }, e: { r: endRow, c: 0 } }); // Sr No.
        merges.push({ s: { r: startRow, c: 1 }, e: { r: endRow, c: 1 } }); // GRN ID
        merges.push({ s: { r: startRow, c: 2 }, e: { r: endRow, c: 2 } }); // Date
      }

      grnSrNo++;
    });

    const worksheet = XLSX.utils.json_to_sheet(excelData);
    worksheet['!merges'] = merges;

    const columnWidths = [
      { wch: 10 }, // Sr No.
      { wch: 10 }, // GRN ID
      { wch: 12 }, // Date
      { wch: 30 }, // Items
      { wch: 15 }, // Batch
      { wch: 10 }, // Quantity
      { wch: 10 }, // UOM
      { wch: 10 }, // Scrap Qty
    ];
    worksheet['!cols'] = columnWidths;

    const range = XLSX.utils.decode_range(worksheet['!ref']);

    for (let r = 0; r <= range.e.r; r++) {
      for (let c = 0; c <= range.e.c; c++) {
        const cellAddress = XLSX.utils.encode_cell({ r, c });
        if (!worksheet[cellAddress]) continue;

        if (typeof worksheet[cellAddress] !== 'object') {
          worksheet[cellAddress] = { v: worksheet[cellAddress] };
        }

        if (r === 0) {
          worksheet[cellAddress].s = {
            font: { bold: true },
            alignment: { horizontal: 'center', vertical: 'center' },
          };
        } else {
          if (c === 0 || c === 1 || c === 2 || c === 4 || c === 5 || c === 6) {
            worksheet[cellAddress].s = {
              alignment: { horizontal: 'center', vertical: 'center' },
            };
          }
        }
      }
    }

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'GRN Data');
    XLSX.writeFile(workbook, 'GRN_Export.xlsx');
  };

  const fetchAndExport = async () => {
    await getAllGRNsForExport();
    exportToExcel(allGrns?.grns);
  };

  const exportItems = [
    {
      key: 1,
      label: 'Export CSV',
      onClick: () => exportToExcel(grns?.results),
      icon: <FileExcelOutlined />,
    },
    {
      key: 2,
      label: 'Export All',
      onClick: fetchAndExport,
      icon: <ExportOutlined />,
    },
  ];

  return (
    <div className="">
      {openSideBar && (
        <GRNSideBar
          openSideBar={openSideBar}
          setOpenSideBar={setOpenSideBar}
          grnId={grnDetails?._id}
        />
      )}
      {openQRModal && (
        <QrModal
          open={openQRModal}
          onClose={() => setOpenQRModal(false)}
          inpages={grnDetails?.inpages}
        />
      )}
      <Header
        title="Stock In"
        description=""
        infoTitle="Welcome to the Stock In Page"
        infoDesc="Your streamlined solution for managing stock-in entries."
        paras={[
          'This page allows you to effortlessly perform "Stock In" actions for parts, raw materials, or products.',
          'With a simple item search, you can complete stock-in entries with ease. Moreover, each entry generates a unique QR code, enhancing user convenience and tracking.',
          'The In Page also provides a detailed stock-in history, ensuring you have all the information you need at your fingertips.',
        ]}
      />

      <div className="flex flex-col w-full gap-4 mb-3 mt-4 md:flex-row md:justify-between md:items-center">
        {/* Left buttons - stack vertically on mobile, horizontal on desktop */}
        <div className="flex flex-wrap gap-2 w-full md:w-auto">
          <Button
            type={currentTab === 'task' ? 'primary' : 'default'}
            onClick={() => setSearchParams({ tab: 'task' })}
            className="flex items-center"
          >
            Task Manager
            <InfoTooltip
              position="bottom"
              id="taskmanager"
              isHtml={true}
              content="When a Purchase Order (PO) is approved, the task manager automatically updates to reflect this status and includes the expected delivery date."
            />
          </Button>
          {defaults?.defaultParam?.projectDefaults?.pendingStockIn && (
            <Badge count={grns?.pendingInpageCount}>
              <Button
                type={currentTab === 'pendingStockIn' ? 'primary' : 'default'}
                onClick={() => setSearchParams({ tab: 'pendingStockIn' })}
                className="flex items-center"
              >
                Pending Stock In
                <InfoTooltip
                  position="bottom"
                  id="pendingStockIn"
                  isHtml={true}
                  content="This Tab Shows the Pending Inpages which not stocked in yet"
                />
              </Button>
            </Badge>
          )}

          <Button
            type={currentTab === 'stockin' ? 'primary' : 'default'}
            onClick={() => setSearchParams({ tab: 'stockin' })}
            className="flex items-center"
          >
            Stocks
          </Button>
        </div>

        {/* Right buttons - search and action buttons */}
        {currentTab === 'stockin' && (
          <div className="flex flex-col gap-2 w-full md:flex-row md:w-auto">
            <FilterIcon
              showFilters={showFilters}
              setShowFilters={setShowFilters}
            />
            <Input.Search
              placeholder="GRN ID or Item Name"
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ width: '100%' }}
              allowClear
              className="rounded-md md:w-60"
            />
            <div className="flex gap-2 w-full md:w-auto">
              <Dropdown
                trigger={['click']}
                placement="bottomRight"
                arrow
                menu={{ items: exportItems }}
              >
                <Button
                  disabled={loading}
                  loading={isExportLoading}
                  icon={<DownloadOutlined />}
                  className="flex-1 md:flex-none"
                  color="green"
                  variant="solid"
                >
                  Export
                </Button>
              </Dropdown>
              <Button
                type="primary"
                onClick={() => navigate('/inventory/inpage/grn')}
                icon={<PlusCircleOutlined />}
                className="flex-1 md:flex-none"
              >
                Create
              </Button>
            </div>
          </div>
        )}
      </div>

      <FilterV2
        config={filterConfig}
        setFilters={setFilters}
        showFilters={showFilters}
      />

      {currentTab === 'stockin' && (
        <>
          <div className="rounded-lg overflow-hidden border border-gray-200">
            <Table
              columns={columns}
              dataSource={grns?.results}
              pagination={false}
              loading={loading}
              rowKey="_id"
              onRow={(record) => ({
                onClick: () => {
                  setOpenSideBar(true);
                  setGrnDetails(record);
                },
                className: 'hover:bg-gray-50 cursor-pointer transition-colors',
              })}
              className="w-full"
              size="small"
              bordered
            />
          </div>

          <div className="mt-4">
            <Pagination
              limit={limit}
              page={page}
              totalPages={grns?.totalPages}
              totalResults={grns?.totalResults}
              setPage={setPage}
              setLimit={setLimit}
            />
          </div>
        </>
      )}

      {currentTab === 'pendingStockIn' && <PendingStockIn />}
      {currentTab === 'task' && <TaskTable />}

      <ReactTooltip id="item-tooltip" place="top" />
      <ReactTooltip id="date-tooltip" place="top" />
    </div>
  );
};

export default GRNTable;
