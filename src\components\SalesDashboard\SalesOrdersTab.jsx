import { <PERSON><PERSON>, But<PERSON>, Empty, Spin } from 'antd';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import { unCamelCaseString } from '../../helperFunction';
import BentoGrid from '../global/components/BentoGrid';
import AreaChart from './charts/AreaChart';
import LineChart from './charts/LineChart';
import PieChart from './charts/PieChart';
import UserStatsLineChart from './charts/UserStatsLineChart';
import MetricsCards from './MetricsCards';
import SalesOrderFormatsTable from './SalesOrderFormatsTable';
import TopCustomers from './TopCustomers';
import TopProducts from './TopProducts';

const SalesOrdersTab = ({
  dateRange,
  selectedCustomer,
  dashboardData,
  topCustomerFilter,
  setTopCustomerFilter,
}) => {
  const [isMobile, setIsMobile] = useState(false);

  const {
    metrics: filteredMetrics = {},
    trendData: filteredTrendData = [],
    statusData: filteredStatusData = [],
    performanceData = [],
    topCustomers: topSalesOrderCustomers = [],
    topProducts = [],
    formats: salesOrderFormats = [],
    isLoadingMetrics = false,
    isLoadingTrend = false,
    isLoadingPerformance = false,
    isLoadingTopCustomers = false,
    isLoadingTopProducts = false,
    isLoadingFormats = false,
    isLoadingSalesOrderUserStats = false,
    metricsError,
    trendError,
    performanceError,
    topCustomersError,
    topProductsError,
    formatsError,
    salesOrderUserStatsError,
    hasData = false,
    selectedMetrics = [],
    updateSelectedMetrics,
    userStats = [],
  } = dashboardData || {};

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const dataAvailable =
    hasData ||
    (filteredTrendData.length > 0 && filteredMetrics?.totalSalesOrders > 0);

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      <MetricsCards
        metrics={filteredMetrics}
        dateRange={dateRange}
        isLoading={isLoadingMetrics}
        type="sales-orders"
        selectedMetrics={selectedMetrics}
        onSelectedMetricsChange={updateSelectedMetrics}
        selectedCustomer={selectedCustomer}
      />

      {metricsError && !isLoadingMetrics && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-16"
        >
          <Alert
            message="Error Loading Data"
            description="There was an error loading the sales orders data. Please try again."
            type="error"
            showIcon
            action={
              <button
                onClick={() => dashboardData?.refetch?.metrics?.()}
                className="text-red-600 hover:text-red-800 font-medium"
              >
                Retry
              </button>
            }
          />
        </motion.div>
      )}

      {!dataAvailable &&
        !isLoadingMetrics &&
        !metricsError &&
        !isLoadingTrend &&
        !trendError && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-16"
          >
            <Empty
              description={
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No data available
                  </h3>
                  <p className="text-gray-600">
                    {(dateRange && dateRange[0] && dateRange[1]) ||
                    selectedCustomer
                      ? `No sales orders found for the selected ${dateRange && dateRange[0] && dateRange[1] ? 'date range' : ''}${dateRange && dateRange[0] && dateRange[1] && selectedCustomer ? ' and ' : ''}${selectedCustomer ? 'customer' : ''}`
                      : 'No sales order data available'}
                  </p>
                </div>
              }
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </motion.div>
        )}

      {(dataAvailable ||
        Object.values({
          isLoadingMetrics,
          isLoadingTrend,
          isLoadingPerformance,
          isLoadingTopCustomers,
          isLoadingTopProducts,
          isLoadingFormats,
        }).some(Boolean)) &&
        !(metricsError && trendError) && (
          <BentoGrid className="sales-orders-grid">
            <BentoGrid.Item
              span={2}
              title="Sales Orders Trend"
              subtitle="Monthly sales order performance"
              index={0}
            >
              {isLoadingTrend ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : trendError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Trend Data"
                    description="Failed to load sales orders trend. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.trend?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : (
                <LineChart
                  data={filteredTrendData}
                  height={isMobile ? 240 : 280}
                  showLegend={true}
                  type="salesOrders"
                />
              )}
            </BentoGrid.Item>

            <BentoGrid.Item
              span={1}
              title="Order Status Distribution"
              subtitle="Current order status"
              index={1}
            >
              {isLoadingMetrics ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : metricsError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Status Data"
                    description="Failed to load status distribution. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.metrics?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : (
                <PieChart
                  data={filteredStatusData}
                  height={isMobile ? 240 : 280}
                  doughnut={true}
                  variant={isMobile ? 'compact' : 'default'}
                  centerText={{
                    value: (filteredMetrics?.totalSalesOrders || 0).toString(),
                    label: 'Total',
                  }}
                />
              )}
            </BentoGrid.Item>
            <BentoGrid.Item
              span={1}
              title="Monthly Performance"
              subtitle="Approved vs Rejected sales orders"
              index={2}
            >
              {isLoadingPerformance ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : performanceError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Performance Data"
                    description="Failed to load performance data. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.performance?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : (
                <AreaChart
                  data={performanceData.map((item) => ({
                    name: item.date,
                    Approved: item.approved,
                    Rejected: item.rejected,
                  }))}
                  height={isMobile ? 240 : 280}
                  showLegend={true}
                  colors={['#059669', '#dc2626']}
                  stacked={false}
                />
              )}
            </BentoGrid.Item>

            <BentoGrid.Item
              span={1}
              index={3}
              title="Top Customers"
              subtitle={`Best customers by ${unCamelCaseString(topCustomerFilter.by)} ${topCustomerFilter.order === 'desc' ? 'Ascending' : 'Descending'}`}
            >
              {isLoadingTopCustomers ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : topCustomersError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Top Customers"
                    description="Failed to load top customers data. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.topCustomers?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : (
                <TopCustomers
                  data={topSalesOrderCustomers}
                  topCustomerFilter={topCustomerFilter}
                  setTopCustomerFilter={setTopCustomerFilter}
                />
              )}
            </BentoGrid.Item>
            <BentoGrid.Item
              span={1}
              index={4}
              title="Top 5 Products"
              subtitle="Best products by total Quotations"
            >
              {isLoadingTopProducts ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : topProductsError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Top Products"
                    description="Failed to load top products data. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.topProducts?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : (
                <TopProducts data={topProducts} isLoading={false} />
              )}
            </BentoGrid.Item>

            <BentoGrid.Item
              span={2}
              index={5}
              title={`${salesOrderFormats.length ? 'Sales Order Formats' : 'Customize This Section'}`}
              subtitle={`${salesOrderFormats.length ? 'Available sales order formats' : 'You can customize this section based on your requirements'}`}
            >
              {isLoadingFormats ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : formatsError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Formats"
                    description="Failed to load sales order formats. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.formats?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : salesOrderFormats && salesOrderFormats.length > 0 ? (
                <SalesOrderFormatsTable
                  data={salesOrderFormats}
                  isLoading={false}
                  dateRange={dateRange}
                  selectedCustomer={selectedCustomer}
                />
              ) : (
                <div className="flex flex-col items-center justify-center p-8 bg-gray-50 rounded-lg">
                  <Empty
                    description={
                      <div className="text-center">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          No Data Available
                        </h3>
                        <p className="text-gray-600 mb-4">
                          You can customize this section based on your
                          requirements.
                        </p>
                        <p className="text-blue-600 hover:text-blue-800">
                          Contact us to customize
                        </p>
                      </div>
                    }
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                </div>
              )}
            </BentoGrid.Item>
            <BentoGrid.Item
              span={4}
              index={6}
              title="Employee Performance"
              subtitle="Employee performance over time"
            >
              {isLoadingSalesOrderUserStats ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : salesOrderUserStatsError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Top Products"
                    description="Failed to load top products data. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.userStats?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : (
                <UserStatsLineChart
                  data={userStats}
                  isLoading={false}
                  height={isMobile ? 240 : 280}
                />
              )}
            </BentoGrid.Item>
          </BentoGrid>
        )}
    </motion.div>
  );
};

export default SalesOrdersTab;
