import { Typography } from 'antd';
import numeral from 'numeral';
import {
  camelCaseString,
  convertNumToWords,
  unCamelCaseString,
} from '../../../helperFunction';
import { useGetFormatByIdQuery } from '../../../slices/productFormatsApiSlice';

const { Title } = Typography;

const QuotationProductFormat = ({ data, showTable, getUpdatedChanges }) => {
  const { productTableFormat } = data;
  const { data: format } = useGetFormatByIdQuery({ id: productTableFormat });

  const { productDetailsFromFormat = [], productChargesFromFormat = {} } =
    data || {};

  const formatValue = (value, field) => {
    if (value === null || value === undefined) return '-';
    if (!field?.formatting) return value;

    if (typeof value === 'number' || !isNaN(parseFloat(value))) {
      const numValue = parseFloat(value);
      const { formatting } = field;

      // Price formatting
      if (formatting.isItPrice) {
        const priceFormat = formatting.roundValue ? '0,0' : '0,0.00';
        const formattedPrice = `₹ ${numeral(numValue).format(priceFormat)}`;

        if (formatting.convertInWords) {
          return (
            <div>
              <div>{formattedPrice}</div>
              <div className="text-sm text-gray-500">
                {convertNumToWords(numValue)} Rupees Only
              </div>
            </div>
          );
        }
        return formattedPrice;
      }

      // Percentage formatting
      if (formatting.isItPercentage) {
        const percentFormat = formatting.roundValue ? '0,0' : '0,0.00';
        return `${numeral(numValue).format(percentFormat)}%`;
      }

      // Default number formatting
      return numeral(numValue).format('0,0');
    }

    return value;
  };

  const getFieldValue = (product, fieldName) => {
    return product[fieldName] || product.customColumns?.[fieldName];
  };

  const isFieldChanged = (index, fieldName) => {
    const changes = getUpdatedChanges('productDetailsFromFormat');
    return (
      changes[index]?.[fieldName] || changes[index]?.customColumns?.[fieldName]
    );
  };

  const renderProductTable = () => {
    if (
      !productDetailsFromFormat.length ||
      !format?.productTablesFormats?.fields
    ) {
      return null;
    }

    const fields = format.productTablesFormats.fields;

    return (
      <div className="overflow-x-auto rounded-md border">
        <table className="min-w-full bg-white border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="px-4 py-2 border text-gray-500">Sr. No</th>
              {fields.map((field) => (
                <th
                  key={field.fieldName}
                  className="px-4 py-2 border uppercase whitespace-nowrap text-gray-500"
                >
                  {field.fieldName}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {productDetailsFromFormat.map((product, index) => (
              <tr
                key={product.key || index}
                style={{ backgroundColor: product?.rowColor || 'white' }}
              >
                <td className="px-4 py-2 border text-center">{index + 1}</td>
                {fields.map((field) => {
                  const fieldName = camelCaseString(field.fieldName);
                  const value = getFieldValue(product, fieldName);
                  const isChanged = isFieldChanged(index, fieldName);

                  return (
                    <td
                      key={field.fieldName}
                      className={`px-4 py-2 border text-left whitespace-nowrap ${
                        isChanged && showTable ? 'text-red-500' : ''
                      }`}
                    >
                      {formatValue(value, field)}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const formatChargeValue = (value, charge) => {
    if (value === null || value === undefined) return '-';
    if (!charge?.formatting) return value;

    if (typeof value === 'number' || !isNaN(parseFloat(value))) {
      const numValue = parseFloat(value);
      const { formatting } = charge;

      // Price formatting
      if (formatting.isItPrice) {
        const priceFormat = formatting.roundValue ? '0,0' : '0,0.00';
        const formattedPrice = `₹ ${numeral(numValue).format(priceFormat)}`;

        if (formatting.convertInWords) {
          return (
            <div>
              <div>{formattedPrice}</div>
              <div className="text-sm text-gray-500">
                {convertNumToWords(numValue)} Rupees Only
              </div>
            </div>
          );
        }
        return formattedPrice;
      }

      // Percentage formatting
      if (formatting.isItPercentage) {
        return `${numeral(numValue).format('0,0')}%`;
      }

      // Default number formatting
      return numeral(numValue).format('0,0.00');
    }

    return value;
  };

  const renderChargesTable = () => {
    if (!Object.keys(productChargesFromFormat).length) return null;

    const charges = Object.entries(productChargesFromFormat).filter(
      ([_, value]) => value !== null && value !== undefined
    );
    const changedCharges = getUpdatedChanges('productChargesFromFormat');

    return (
      <div className="mt-4 flex justify-end">
        <table className="min-w-[300px] bg-white border border-gray-300 rounded-xl">
          <thead>
            <tr className="bg-gray-100">
              <th className="px-4 py-2 border text-gray-500 uppercase">
                Charges
              </th>
              <th className="px-4 py-2 border text-gray-500 uppercase">
                Amount
              </th>
            </tr>
          </thead>
          <tbody>
            {charges.map(([key, value]) => {
              const charge = format?.productTablesFormats?.charges?.find(
                (c) => camelCaseString(c.chargeName) === key.replace(/_/g, ' ')
              );
              const isChanged = changedCharges[key];

              return (
                <tr key={key}>
                  <td className="px-4 py-2 border uppercase">
                    {unCamelCaseString(key)}
                  </td>
                  <td
                    className={`px-4 py-2 border text-left ${
                      isChanged && showTable ? 'text-red-500' : ''
                    }`}
                  >
                    {formatChargeValue(value, charge)}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="mt-4">
      <Title level={5}>Product Details</Title>
      {renderProductTable()}
      {renderChargesTable()}
    </div>
  );
};

export default QuotationProductFormat;
