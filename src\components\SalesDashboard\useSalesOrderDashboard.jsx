import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { toast } from 'react-toastify';
import {
  useLazyGetDashboardPreferencesQuery,
  useLazyGetSalesOrdersCreatedByDataQuery,
  useLazyGetSalesOrdersFormatsQuery,
  useLazyGetSalesOrdersMetricsQuery,
  useLazyGetSalesOrdersPerformanceQuery,
  useLazyGetSalesOrdersTopCustomersQuery,
  useLazyGetSalesOrdersTopProductsQuery,
  useLazyGetSalesOrdersTrendQuery,
  useSaveDashboardPreferencesMutation,
} from '../../slices/salesDashboardApiSlice';

const useSalesOrderDashboard = ({
  dateRange,
  selectedCustomer,
  topCustomerFilter,
  isActive = false,
  onDateRangeChange,
  onSelectedCustomerChange,
}) => {
  const [getPreferences] = useLazyGetDashboardPreferencesQuery();
  const [saveDashboardPreferences] = useSaveDashboardPreferencesMutation();
  const [selectedMetrics, setSelectedMetrics] = useState([
    'totalSalesOrders',
    'approvedSalesOrders',
    'rejectedSalesOrders',
    'pendingSalesOrders',
    'avgDaysToApproval',
  ]);
  const [preferencesLoaded, setPreferencesLoaded] = useState(false);
  const isSavingRef = useRef(false);
  const lastSavedPreferencesRef = useRef(null);

  const [
    getMetrics,
    { data: metricsData, isLoading: isLoadingMetrics, error: metricsError },
  ] = useLazyGetSalesOrdersMetricsQuery();
  const [
    getTrend,
    { data: trendData, isLoading: isLoadingTrend, error: trendError },
  ] = useLazyGetSalesOrdersTrendQuery();

  const [
    getPerformance,
    {
      data: performanceData,
      isLoading: isLoadingPerformance,
      error: performanceError,
    },
  ] = useLazyGetSalesOrdersPerformanceQuery();

  const [
    getTopCustomers,
    {
      data: topCustomersData,
      isLoading: isLoadingTopCustomers,
      error: topCustomersError,
    },
  ] = useLazyGetSalesOrdersTopCustomersQuery();

  const [
    getTopProducts,
    {
      data: topProductData,
      isLoading: isLoadingTopProducts,
      error: topProductsError,
    },
  ] = useLazyGetSalesOrdersTopProductsQuery();
  const [
    getSalesOrdersCreatedByData,
    {
      data: salesOrderUserStatsData,
      isLoading: isLoadingSalesOrderUserStats,
      error: salesOrderUserStatsError,
    },
  ] = useLazyGetSalesOrdersCreatedByDataQuery();

  const [
    getFormats,
    { data: formatsData, isLoading: isLoadingFormats, error: formatsError },
  ] = useLazyGetSalesOrdersFormatsQuery();

  const queryParams = useMemo(() => {
    const params = {};

    if (dateRange && dateRange[0] && dateRange[1]) {
      params.dateRange = {
        startDate: dayjs(dateRange[0]).format('YYYY-MM-DD'),
        endDate: dayjs(dateRange[1]).format('YYYY-MM-DD'),
      };
    }

    if (selectedCustomer) {
      params.customerId = selectedCustomer;
    }
    if (topCustomerFilter) {
      params.topCustomerFilter = topCustomerFilter;
    }

    return params;
  }, [dateRange, selectedCustomer, topCustomerFilter]);

  // Load preferences on mount
  useEffect(() => {
    const loadPreferences = async () => {
      if (!isActive) return;

      try {
        const response = await getPreferences({ tabType: 'sales-orders' });
        if (response.data) {
          const {
            selectedMetrics: savedMetrics,
            dateRange: savedDateRange,
            selectedCustomer: savedCustomer,
          } = response.data;

          if (savedMetrics && savedMetrics.length > 0) {
            setSelectedMetrics(savedMetrics);
          }

          if (
            savedDateRange &&
            (savedDateRange.startDate || savedDateRange.endDate)
          ) {
            const newDateRange = [
              savedDateRange.startDate ? dayjs(savedDateRange.startDate) : null,
              savedDateRange.endDate ? dayjs(savedDateRange.endDate) : null,
            ];
            if (onDateRangeChange) {
              onDateRangeChange(newDateRange);
            }
          }

          if (savedCustomer && onSelectedCustomerChange) {
            onSelectedCustomerChange(savedCustomer);
          }
        }
      } catch (error) {
        // No saved preferences found - use defaults
      } finally {
        setPreferencesLoaded(true);
      }
    };

    if (!preferencesLoaded && isActive) {
      loadPreferences();
    }
  }, [
    isActive,
    preferencesLoaded,
    getPreferences,
    onDateRangeChange,
    onSelectedCustomerChange,
  ]);

  const fetchAllData = useCallback(async () => {
    if (!isActive || !preferencesLoaded) return;

    try {
      await Promise.all([
        getMetrics(queryParams),
        getTrend(queryParams),
        getPerformance(queryParams),
        getTopCustomers(queryParams),
        getTopProducts({ dateRange: queryParams.dateRange }),
        getFormats(queryParams),
        getSalesOrdersCreatedByData(queryParams),
      ]);
    } catch (error) {
      toast.error('Error fetching sales order dashboard data');
    }
  }, [
    isActive,
    preferencesLoaded,
    queryParams,
    getMetrics,
    getTrend,
    getPerformance,
    getTopCustomers,
    getTopProducts,
    getFormats,
    getSalesOrdersCreatedByData,
  ]);

  const hasData = useMemo(() => {
    return !!(
      metricsData?.totalSalesOrders > 0 ||
      (trendData && trendData.length > 0)
    );
  }, [metricsData, trendData]);

  const transformedStatusData = useMemo(() => {
    if (!metricsData) return [];

    return [
      {
        name: 'Approved',
        value: metricsData.approvedSalesOrders || 0,
        color: '#059669',
      },
      {
        name: 'Rejected',
        value: metricsData.rejectedSalesOrders || 0,
        color: '#dc2626',
      },
      {
        name: 'Pending',
        value: metricsData.pendingSalesOrders || 0,
        color: '#d97706',
      },
    ];
  }, [metricsData]);

  const savePreferences = useCallback(
    async (newPreferences) => {
      if (isSavingRef.current) return;

      const preferencesString = JSON.stringify(newPreferences);
      if (lastSavedPreferencesRef.current === preferencesString) return;

      try {
        isSavingRef.current = true;

        await saveDashboardPreferences({
          tabType: 'sales-orders',
          preferences: {
            selectedMetrics:
              newPreferences.selectedMetrics !== undefined
                ? newPreferences.selectedMetrics
                : selectedMetrics,
            dateRange:
              newPreferences.dateRange !== undefined
                ? newPreferences.dateRange
                : {
                    startDate:
                      dateRange && dateRange[0] ? dateRange[0].toDate() : null,
                    endDate:
                      dateRange && dateRange[1] ? dateRange[1].toDate() : null,
                  },
            selectedCustomer:
              newPreferences.selectedCustomer !== undefined
                ? newPreferences.selectedCustomer
                : selectedCustomer,
          },
        }).unwrap();

        lastSavedPreferencesRef.current = preferencesString;
      } catch (error) {
        toast.error('Failed to save dashboard preferences');
      } finally {
        isSavingRef.current = false;
      }
    },
    [selectedMetrics, dateRange, selectedCustomer, saveDashboardPreferences]
  );

  const refetch = useMemo(
    () => ({
      metrics: () => getMetrics(queryParams),
      trend: () => getTrend(queryParams),
      performance: () => getPerformance(queryParams),
      topCustomers: () => getTopCustomers({ dateRange: queryParams.dateRange }),
      topProducts: () => getTopProducts({ dateRange: queryParams.dateRange }),
      formats: () => getFormats(queryParams),
      userStats: () => getSalesOrdersCreatedByData(queryParams),
    }),
    [
      queryParams,
      getMetrics,
      getTrend,
      getPerformance,
      getTopCustomers,
      getTopProducts,
      getFormats,
      getSalesOrdersCreatedByData,
    ]
  );

  return {
    // Data
    metrics: metricsData,
    trendData: trendData || [],
    statusData: transformedStatusData,
    performanceData: performanceData || [],
    topCustomers: topCustomersData || [],
    topProducts: topProductData || [],
    formats: formatsData || [],
    userStats: salesOrderUserStatsData || [],

    // Loading States
    isLoadingMetrics,
    isLoadingTrend,
    isLoadingPerformance,
    isLoadingTopCustomers,
    isLoadingTopProducts,
    isLoadingFormats,
    isLoadingSalesOrderUserStats,

    // Error States
    metricsError,
    trendError,
    performanceError,
    topCustomersError,
    topProductsError,
    formatsError,
    salesOrderUserStatsError,

    hasData,

    // Metrics preferences
    selectedMetrics,
    updateSelectedMetrics: setSelectedMetrics,

    // Preferences
    savePreferences,

    // Actions
    fetchAllData,
    refetch,
  };
};

export default useSalesOrderDashboard;
