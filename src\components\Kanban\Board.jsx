import { useEffect, useRef } from 'react';
import Column from './Column';

function Board({
  isMobile,
  isTablet,
  data,
  kanbanDataColumnWise,
  // refetch,
  adminView = false,
  dep = [],
  setHistorySidebar,
  initialScrollIndex = 0,
  visibleTiles,
  setInitialScrollIndex,
}) {
  const scrollRef = useRef(null);
  useEffect(() => {
    if (
      scrollRef.current &&
      initialScrollIndex >= 0 &&
      initialScrollIndex < data?.length
    ) {
      let columnWidth = 0;
      for (let i = 0; i < initialScrollIndex; i++) {
        columnWidth += scrollRef.current.childNodes[i].offsetWidth + 12;
      }

      scrollRef.current.scrollTo({
        left: columnWidth,
        behavior: 'smooth',
      });
    }
  }, [initialScrollIndex, data]);
  return (
    <>
      <div
        className="!w-full flex gap-x-[7px] !scroll-smooth overflow-scroll !mt-5 md:!mt-0"
        ref={scrollRef}
      >
        {data?.map((item, idx) => {
          return (
            <div
              key={idx}
              className={`${visibleTiles?.includes(item?.page?.label) ? '' : 'hidden'}`}
            >
              <Column
                isMobile={isMobile}
                isTablet={isTablet}
                key={idx}
                index={idx}
                column={item}
                // refetch={refetch}
                kanbanDataColumnWise={kanbanDataColumnWise}
                adminView={adminView}
                dep={dep[idx]}
                setHistorySidebar={setHistorySidebar}
                setInitialScrollIndex={setInitialScrollIndex}
              />
            </div>
          );
        })}
      </div>
    </>
  );
}

export default Board;
