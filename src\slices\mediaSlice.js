import { apiSlice } from './apiSlice';

const baseRoute = '/v1/media';

const mediaApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getAllMedia: builder.query({
      query: () => baseRoute + '/',
    }),
    getMediaById: builder.query({
      query: ({ id }) => baseRoute + '/' + id,
    }),
    getMediaMeta: builder.query({
      query: () => baseRoute + '/meta',
    }),
    getMediaByIdArray: builder.mutation({
      query: ({ data }) => ({
        url: baseRoute + '/kanban/getAll',
        method: 'POST',
        body: data,
      }),
      // invalidatesTags: ['MachineIds'],
    }),
    editMediaById: builder.mutation({
      query: ({ id, data }) => ({
        url: `${baseRoute}/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['QuotationTemplate', 'AllQuotationTemplate'],
    }),
    kanbanFormMediaHandler: builder.mutation({
      query: ({ data }) => ({
        url: baseRoute + '/kanban/save',
        method: 'POST',
        body: data,
      }),
    }),
  }),
});

export const {
  useLazyGetMediaByIdQuery,
  useGetMediaByIdQuery,
  useGetMediaMetaQuery,
  useGetAllMediaQuery,
  useGetMediaByIdArrayMutation,
  useEditMediaByIdMutation,
  useKanbanFormMediaHandlerMutation,
} = mediaApiSlice;
