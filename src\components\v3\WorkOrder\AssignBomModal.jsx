import { useEffect, useState } from 'react';
import { Select, Table, Dropdown } from 'antd';
import Button from '../../global/components/Button';
import FullScreenModal from '../../global/components/FullScreenModal';
import RMModal from './RMModal';

import {
  useGetBomByIdQuery,
  useGetBomsForWoOptionsQuery,
} from '../../../slices/assemblyBomApiSlice';

const AssignBomModal = ({
  openModal,
  setOpenModal,
  allParts,
  allProducts,
  orderQuantity,
  setRows,
  rowIndex,
  record,
  onClose,
  templateOption = [],
}) => {
  const [selectedBomId, setSelectedBomId] = useState('');
  const [bomItems, setBomItems] = useState({});
  const [rmModal, setRmModal] = useState({
    open: false,
    record: {},
    recordIndex: -1,
  });

  const { data: bomOptions } = useGetBomsForWoOptionsQuery({});
  const { data: bom } = useGetBomByIdQuery({
    id: selectedBomId,
  });

  const subtractReserved = (data) => {
    if (!data?.quantity) return 0;
    const diff = data?.quantity - (data?.reserved || 0);
    return (diff <= 0 ? 0 : diff) || 0;
  };

  const getInStockInteger = (record) => {
    let all = [...allParts, ...allProducts];
    let item = all?.find((elem) => elem?.value === record?._id);
    const diff = item?.quantity - (item?.reserved || 0);
    return (diff <= 0 ? 0 : diff) || 0;
  };

  const getInStock = (record) => {
    let all = [...allParts, ...allProducts];
    let item = all?.find((elem) => elem?.value === record?._id);
    return `${subtractReserved(item) || 0}\xA0(${
      item?.quantity?.toFixed(2) || 0
    },\xA0${item?.reserved?.toFixed(2) || 0})`;
  };

  useEffect(() => {
    if (record?.bom?.bomId) {
      setSelectedBomId(record?.bom?.bomId);
      // setBomItems(record?.bom?.item);
    }
  }, [record?.bom?.bomId, record?.bom?.item]);

  function collectBomItems(bom) {
    // const items = [];
    let newItem = [];

    function traverse(node, units) {
      let totalUnits = units * (node?.units || 1);

      if (node?.part) {
        // items.push({
        //   units: node?.units,
        //   rawMaterials: node?.rawMaterials?.map((item) => ({
        //     label: item?.label,
        //     value: item?.value,
        //     units: item?.units,
        //     type: item?.type,
        //   })),
        //   rowCategory: node?.category,
        //   rowId: node?.rowId,
        //   ...node?.part,
        //   totalUnits,
        // });
        newItem.push({
          indexId: node?._id,
          units: +node?.units,
          rawMaterials: node?.rawMaterials?.map((item) => ({
            label: item?.item?.name || item?.label,
            name: item?.item?.name || item?.name,
            value: item?.item?._id || item?.value,
            units: item?.units,
            type: item?.partType || item?.type,
          })),
          item: node?.part?._id || node?.part,
          itemName: node?.part?.name,
          itemType: 'Part',
          category: node?.part?.category,
          multiplier: +totalUnits,
          itemData: node?.part,
          rowCategory: node?.category,
          rowId: node?.rowId,
          jobTemplate: node?.template ? node?.template : null,
        });
      }

      if (node?.product) {
        // items.push({
        //   units: node?.units,
        //   rawMaterials: node?.rawMaterials?.map((item) => ({
        //     label: item?.label,
        //     value: item?.value,
        //     units: item?.units,
        //     type: item?.type,
        //   })),
        //   rowCategory: node?.category,
        //   rowId: node?.rowId,
        //   ...node?.product,
        //   totalUnits,
        // });
        newItem.push({
          indexId: node?._id,
          units: +node?.units,
          rawMaterials: node?.rawMaterials?.map((item) => ({
            label: item?.item?.name || item?.label,
            name: item?.item?.name || item?.name,
            value: item?.item?._id || item?.value,
            units: item?.units,
            type: item?.partType || item?.type,
          })),

          item: node?.product?._id || node?.product,
          itemName: node?.product?.name,
          itemType: 'Product',
          category: node?.product?.category,
          multiplier: +totalUnits,
          itemData: node?.product,
          rowCategory: node?.category,
          rowId: node?.rowId,
          jobTemplate: node?.template ? node?.template : null,
        });
      }

      if (node.children && Array.isArray(node.children)) {
        node.children.forEach((child) =>
          traverse(
            child,
            totalUnits !== undefined && isNaN(totalUnits) !== true
              ? totalUnits
              : 1
          )
        );
      }
    }
    traverse(bom, 1);

    return newItem;
  }

  useEffect(() => {
    if (bom?._id) {
      // let { items: temp, newItem } = collectBomItems(bom);
      let newItem = collectBomItems(bom);

      // setRows((prev) => {
      //   let items = [...prev];
      //   items[rowIndex] = {
      //     ...items[rowIndex],
      //     bom: {
      //       bomId: bom?._id,
      //       item: temp,
      //     },
      //   };
      //   return items;
      // });

      // setBomItems(temp);
      setBomItems(newItem);
    }
  }, [bom?._id, record?.bom?.bomId, selectedBomId]); //eslint-disable-line

  const columns = [
    {
      title: 'Sr No.',
      dataIndex: 'serialNo',
      key: 'serialNo',
      render: (_, record) => <span>{record?.rowId}</span>,
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (_, record) => (
        <>
          <span>{record?.rowCategory?.toUpperCase()}</span>
        </>
      ),
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => (
        <>
          <span>{record?.itemName}</span>
        </>
      ),
    },
    {
      title: 'Units',
      dataIndex: 'units',
      key: 'units',
      render: (_, record) => <span>{record?.units}</span>,
    },
    {
      title: 'In Stock',
      dataIndex: 'inStock',
      key: 'inStock',
      render: (_, record) => <span>{getInStock(record?.itemData || {})}</span>,
    },
    {
      title: 'Required Stock',
      dataIndex: 'requiredStock',
      key: 'requiredStock',
      render: (_, record) => {
        return (
          <span>
            {(record?.multiplier * orderQuantity -
              getInStockInteger(record?.itemData) <
            0
              ? 0
              : record?.multiplier * orderQuantity -
                getInStockInteger(record?.itemData)) || 0}
          </span>
        );
      },
    },
    {
      title: 'Actions',
      dataIndex: 'actions',
      key: 'actions',
      render: (_, record, index) => {
        let items = templateOption
          ?.filter((elem) => elem?.isForWorkOrder)
          ?.map((elem) => ({
            label: elem?.name,
            key: elem?._id,
          }));
        const menuProps = {
          items,
          onClick: (e) => {
            setBomItems((prev) => {
              let items = [...prev];
              items[index] = {
                ...items[index],
                jobTemplate: e.key,
              };
              return items;
            });
          },
        };
        return (
          <div>
            {record?.rowCategory === 'inhouse' && (
              <div className="grid grid-cols-2 ">
                <Dropdown.Button
                  menu={menuProps}
                  arrow={true}
                  type="primary"
                  size="medium"
                >
                  {
                    templateOption?.find(
                      (elem) => elem?._id === record?.jobTemplate
                    )?.name
                  }
                </Dropdown.Button>
                <Button
                  className="bg-orange-500 text-nowrap py-1 text-sm"
                  onClick={() => {
                    setRmModal({
                      open: true,
                      record: record,
                      recordIndex: index,
                    });
                  }}
                >
                  + Select RM
                </Button>
              </div>
            )}
          </div>
        );
      },
    },
  ];

  const submitBom = () => {
    if (bomItems?.length === 0) {
      clearBom();
    } else {
      setRows((prev) => {
        return prev?.map((elem, index) => {
          if (index === rowIndex) {
            return {
              ...elem,
              bom: {
                bomId: selectedBomId,
                item: bomItems?.map((elem) => {
                  const { itemData, ...rest } = elem; //eslint-disable-line
                  return rest;
                }),
              },
            };
          } else {
            return elem;
          }
        });
      });
      onClose();
      setSelectedBomId('');
      setBomItems({});
    }
  };

  const clearBom = () => {
    setBomItems([]);
    setSelectedBomId('');
    setRows((prev) => {
      return prev?.map((elem, index) => {
        if (index === rowIndex) {
          delete elem.bom;
          return elem;
        } else {
          return elem;
        }
      });
    });
  };

  return (
    <>
      <RMModal
        openModal={rmModal?.open}
        setOpenModal={setRmModal}
        record={rmModal?.record}
        recordIndex={rmModal?.recordIndex}
        setBomItems={setBomItems}
        allParts={allParts}
        orderQuantity={
          rmModal?.record?.multiplier * orderQuantity -
            getInStockInteger(rmModal?.record?.itemData) <
          0
            ? 0
            : rmModal?.record?.multiplier * orderQuantity -
              getInStockInteger(rmModal?.record?.itemData)
        }
      />
      <FullScreenModal
        ShowModal={openModal}
        setShowModal={setOpenModal}
        title="Assign Bom to Item"
        onOk={submitBom}
        onClose={() => {
          clearBom();
          onClose();
        }}
      >
        <Select
          virtual={false}
          className="w-full"
          allowClear={true}
          options={bomOptions?.map((elem) => ({
            label: elem?.name,
            value: elem._id,
          }))}
          onChange={(e) => {
            if (e) {
              setSelectedBomId(e);
            }
          }}
          onClear={clearBom}
          value={selectedBomId}
        />
        {bomItems?.length > 0 && (
          <div className="w-full mt-2">
            <Table
              columns={columns}
              dataSource={bomItems}
              rowKey="_id"
              pagination={false}
              scroll={{ x: 'max-content' }}
              className="shadow-md rounded-lg"
              expandable={{
                expandedRowRender: () => null,
                rowExpandable: () => false,
                expandIcon: () => null,
              }}
            />
          </div>
        )}
      </FullScreenModal>
    </>
  );
};

export default AssignBomModal;
