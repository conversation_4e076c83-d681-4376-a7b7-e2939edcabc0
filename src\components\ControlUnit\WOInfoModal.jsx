import { useState } from 'react';

import { Modal } from 'antd';

import Button from '../global/components/Button';
import EllipsisText from '../global/components/EllipsesText';
import Table from '../global/components/Table';
import RenderAdditionalField from '../Templates/RenderAdditionalField';
import ItemModalV3 from '../v3/WorkOrder/ItemModal';

import { useGetAllPartsForOptionsQuery } from '../../slices/partApiSlice';
import { useGetAllProductsForOptionsQuery } from '../../slices/productApiSlice';

const WOInfoModal = ({
  setShowItem,
  setItemData,
  showItem,
  selectedWo,
  itemData,
}) => {
  const { data: allParts = [] } = useGetAllPartsForOptionsQuery();
  const { data: allProducts = [] } = useGetAllProductsForOptionsQuery();

  const [bomToShow, setBomToShow] = useState({
    bom: {},
    open: false,
  });
  const [rmToShow, setRMToShow] = useState({
    rawMaterials: [],
    assets: [],
    orderQuantity: 0,
    open: false,
  });

  const closeBomModal = () => {
    setBomToShow({
      bom: {},
      open: false,
    });
  };

  const closeRMModal = () => {
    setRMToShow({
      rawMaterials: [],
      open: false,
    });
  };

  return (
    <Modal
      title="Material Details"
      onCancel={() => {
        setShowItem(false);
        setItemData(null);
      }}
      isSubmitRequired={false}
      canSubmit={false}
      centered
      open={showItem}
      footer={null}
      width="60%"
      styles={{
        body: {
          height: '80%',
          overflowY: 'auto',
        },
      }}
    >
      <section>
        {selectedWo?.type === 'Assembly' && (
          <p className="flex items-center justify-between font-semibold">
            Item Name : <span className="font-normal">{itemData?.name}</span>{' '}
          </p>
        )}
        <p className="flex items-center justify-between font-semibold">
          Item :{' '}
          <span className="font-normal">
            {itemData?.category?.toUpperCase()}
          </span>{' '}
        </p>
        <p className="flex items-center justify-between font-semibold">
          Part/Product Name :{' '}
          <EllipsisText text={itemData?.itemId?.name} className="font-normal" />
        </p>

        <p className="flex items-center justify-between font-semibold">
          Item Unit : <span className="font-normal">{itemData?.units}</span>{' '}
        </p>
      </section>
      <section>
        {selectedWo?.additionalFields?.templateData?.length > 0 && (
          <RenderAdditionalField
            additionalFields={selectedWo?.additionalFields}
          />
        )}
      </section>
      {selectedWo?.items === undefined ? (
        <section className=" mt-3">
          {itemData?.category === 'inhouse' ? (
            <>
              <Table>
                <Table.Head>
                  <Table.Row>
                    <Table.Th>Name</Table.Th>
                    <Table.Th>Type</Table.Th>
                    <Table.Th>Units</Table.Th>
                    <Table.Th></Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {itemData?.rawMaterials?.map((data, dIdx) => (
                    <Table.Row key={dIdx}>
                      <Table.Td>{data?.item?.name}</Table.Td>
                      <Table.Td>{(data?.partType).toUpperCase()}</Table.Td>
                      <Table.Td>{data?.units}</Table.Td>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </>
          ) : (
            <>
              <Table>
                <Table.Head>
                  <Table.Row>
                    <Table.Th>Name</Table.Th>
                    <Table.Th>Type</Table.Th>
                    <Table.Th>Units</Table.Th>
                    <Table.Th></Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {itemData?.children?.map((data, dIdx) => (
                    <Table.Row key={dIdx}>
                      <Table.Td>{data?.name}</Table.Td>
                      <Table.Td>{(data?.category).toUpperCase()}</Table.Td>
                      <Table.Td>{data?.units}</Table.Td>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </>
          )}
        </section>
      ) : (
        <section className="flex items-center justify-between font-semibold">
          <ItemModalV3
            items={bomToShow?.bom}
            showModal={bomToShow?.open}
            orderQuantity={bomToShow?.orderQuantity}
            closeModal={closeBomModal}
            allParts={allParts}
            allProducts={allProducts}
            type="bom"
          />
          <ItemModalV3
            items={rmToShow?.rawMaterials}
            assets={rmToShow?.assets}
            showModal={rmToShow?.open}
            orderQuantity={rmToShow?.orderQuantity}
            closeModal={closeRMModal}
            allParts={allParts}
            allProducts={allProducts}
            type="rm"
          />
          <p>BOM/RM Details</p>
          <Button
            type="primary"
            onClick={() => {
              if (itemData?.bom?.item?.length > 0) {
                setBomToShow({
                  bom: itemData?.bom,
                  orderQuantity: itemData?.units * selectedWo?.orderQuantity,
                  open: true,
                });
              }
              if (itemData?.rawMaterials?.length > 0) {
                setRMToShow({
                  rawMaterials: itemData?.rawMaterials || [],
                  assets: itemData?.assetData || [],
                  orderQuantity: itemData?.units,
                  open: true,
                });
              }
            }}
          >
            Show
          </Button>
        </section>
      )}
    </Modal>
  );
};

export default WOInfoModal;
