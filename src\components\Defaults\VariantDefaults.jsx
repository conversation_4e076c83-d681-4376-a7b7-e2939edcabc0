import Button from '../global/components/Button';
import Input from '../global/components/Input';

const VariantDefaults = ({ defaults, setDefaults }) => {
  const handleDeleteVariantLabel = (indexToDelete) => {
    setDefaults((prev) => ({
      ...prev,
      projectDefaults: {
        ...prev.projectDefaults,
        variantLabels: prev?.projectDefaults?.variantLabels?.filter(
          (_, idx) => idx !== indexToDelete
        ),
      },
    }));
  };

  return (
    <div className="w-[100%] border-b-2 text-sm  my-3 border-gray-400/70">
      <h3 className="text-gray-subHeading">Variant Defaults :</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 w-full py-2 justify-center items-center gap-x-5 gap-y-1">
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="enableMultiselectVariants"
            className="w-4 h-4"
            checked={
              defaults?.projectDefaults?.enableMultiselectVariants || false
            }
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  enableMultiselectVariants: e.target.checked,
                },
              }));
            }}
          />
          <label
            htmlFor="enableMultiselectVariants"
            className="ml-2 text-sm font-medium text-gray-700"
          >
            Enable Multiselect for Variants
          </label>
        </div>
        <div>
          <label
            htmlFor="noOfParentNames"
            className="text-sm font-medium text-gray-700"
          >
            Number of Parent Names
          </label>
          <Input
            type="number"
            id="noOfParentNames"
            className="w-3/5 mt-2"
            value={defaults?.projectDefaults?.noOfParentNames || 0}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  noOfParentNames: +e.target.value,
                },
              }));
            }}
          />
        </div>{' '}
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="enableHorizontalSelector"
            className="w-4 h-4"
            checked={
              defaults?.projectDefaults?.enableHorizontalSelector || false
            }
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  enableHorizontalSelector: e.target.checked,
                },
              }));
            }}
          />
          <label
            htmlFor="enableHorizontalSelector"
            className="ml-2 text-sm font-medium text-gray-700"
          >
            Enable Horizontal Selector for Variants
          </label>
        </div>
        <div className="col-span-full">
          <label
            htmlFor="noOfParentNames"
            className="text-sm font-medium text-gray-700"
          >
            Variant labels
          </label>
          <div className="flex items-center gap-4 w-full flex-wrap">
            {(defaults?.projectDefaults?.variantLabels || [])?.map((i, idx) => (
              <div key={idx} className="flex items-center gap-2">
                <Input
                  className={'!min-w-60'}
                  value={i}
                  onChange={(e) => {
                    setDefaults((prev) => ({
                      ...prev,
                      projectDefaults: {
                        ...prev.projectDefaults,
                        variantLabels:
                          prev?.projectDefaults?.variantLabels?.map(
                            (val, vIdx) => {
                              if (vIdx === idx) return e?.target?.value;
                              return val;
                            }
                          ),
                      },
                    }));
                  }}
                />
                <Button
                  className="!min-w-fit px-2"
                  onClick={() => handleDeleteVariantLabel(idx)}
                >
                  ✕
                </Button>
              </div>
            ))}
            <Button
              className={'!min-w-60'}
              onClick={() => {
                setDefaults((prev) => ({
                  ...prev,
                  projectDefaults: {
                    ...prev.projectDefaults,
                    variantLabels: [
                      ...(prev?.projectDefaults?.variantLabels || []),
                      '',
                    ],
                  },
                }));
              }}
            >
              Add
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VariantDefaults;
