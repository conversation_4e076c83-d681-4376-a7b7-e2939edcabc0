import { useState } from 'react';
import RSelect from 'react-select';

const defaultStyles = {
  control: (provided) => {
    return {
      ...provided,
      borderRadius: '6px',
      border: '1px solid #C8CEE1',
      // padding: '0rem 0.2rem',
      fontSize: '14px',
      minHeight: '23px',
      height: '31px',
      width: '100%',
      minWidth: '7rem',
      textOverflow: 'ellipsis',
    };
  },
  menuList: (provided) => {
    return {
      ...provided,
      maxHeight: '180px',
    };
  },
  // menuPortal: (base) => ({ ...base, zIndex: 9999 }),
  placeholder: (provided) => ({
    ...provided,
    fontSize: '14px', // Adjust font size
  }),
  valueContainer: (provided) => ({
    ...provided,
    position: 'relative',
    top: '-2px',
  }),
  dropdownIndicator: (provided) => ({
    ...provided,
    padding: '0 4px',
    position: 'relative',
    top: '-2px',
  }),
};

const Select = ({
  value,
  onChange,
  name = '',
  options,
  placeholder = 'Select',
  id,
  styles,
  className,
  disabled,
  onFocus,
  onBlur,
  isLoading = false,
  menuPlacement = 'auto',
  menuPosition = 'absolute',
  usePortal = false,
  portalTarget,
  ...rest
}) => {
  const [focused, setFocused] = useState(false);

  const val = options?.find((op) => op?.value === value);

  return (
    <RSelect
      menuPlacement={menuPlacement}
      {...rest}
      isDisabled={disabled}
      onFocus={() => {
        setFocused(true);
        if (onFocus) onFocus();
      }}
      onBlur={() => {
        setFocused(false);
        if (onBlur) onBlur();
      }}
      styles={{
        ...defaultStyles,
        ...styles,
      }}
      options={options?.map((op) => ({
        label: op?.name || op?.label,
        value: op?.value,
        isDisabled: op?.disabled,
      }))}
      value={
        val ? { label: val?.name || val?.label, value: val?.value } : false
      }
      onChange={(e) =>
        onChange({
          target: { label: e?.label, name, value: e?.value, type: 'select' },
        })
      }
      components={{
        IndicatorSeparator: () => null,
      }}
      id={id}
      placeholder={placeholder}
      className={`text-start w-full ${focused ? 'z-10' : ''} ${className}`}
      isLoading={isLoading}
      menuPortalTarget={usePortal ? portalTarget || document.body : null}
      menuPosition={menuPosition}
    />
  );
};

export default Select;
