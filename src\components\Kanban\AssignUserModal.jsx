import { useState } from 'react';
import { toast } from 'react-toastify';
import { useUpdateAssignUsersMutation } from '../../slices/orderApiSlice';

import Button from '../global/components/Button';
import MultiSelect from '../global/components/MultiSelect';

const AssignUserModal = ({ setUserModal, card, user, employees }) => {
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [updateAssignUsers, { isLoading }] = useUpdateAssignUsersMutation();

  const handleUpdateAssignUser = async () => {
    let adminId;
    if (user.role === 'admin' || user.role === 'superuser') {
      adminId = user?._id;
    }
    const res = await updateAssignUsers({
      selectedUsers: selectedUsers,
      id: card?._id,
      adminId: adminId,
    });
    if (res) {
      toast.success('Assigned User Successfully');
      setUserModal(false);
    }
  };

  return (
    <div
      onClick={() => setUserModal(false)}
      className="fixed inset-0 flex items-center justify-center z-50 bg-gray-900 bg-opacity-50"
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="flex flex-col justify-around elative w-[420px] bg-white border border-gray-200 shadow-md rounded-lg p-2"
      >
        <section className="w-full flex flex-col">
          <label
            className="mb-1"
            style={{ fontSize: '13px', fontWeight: 'bold' }}
            htmlFor="projectId"
          >
            Assign Users
          </label>
          <MultiSelect
            closeMenuOnSelect={false}
            className="!w-full"
            placeholder="Assign Users"
            options={employees?.map((employee) => ({
              value: employee?._id,
              label: employee?.name,
            }))}
            value={selectedUsers}
            onChange={(e) => {
              setSelectedUsers(e.target.value);
            }}
          />
        </section>
        <div className="flex items-center justify-end">
          <Button
            className="mt-4 !py-1"
            isLoading={isLoading}
            onClick={handleUpdateAssignUser}
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AssignUserModal;
