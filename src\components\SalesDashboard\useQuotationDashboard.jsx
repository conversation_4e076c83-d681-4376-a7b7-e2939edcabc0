import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { toast } from 'react-toastify';
import {
  useLazyGetDashboardPreferencesQuery,
  useLazyGetQuotationsCreatedByDataQuery,
  useLazyGetQuotationsFormatsQuery,
  useLazyGetQuotationsMetricsQuery,
  useLazyGetQuotationsTopCustomersQuery,
  useLazyGetQuotationsTopProductsQuery,
  useLazyGetQuotationsTrendQuery,
  useLazyGetQuotationToSalesOrderConversionQuery,
  useSaveDashboardPreferencesMutation,
} from '../../slices/salesDashboardApiSlice';

const useQuotationDashboard = ({
  dateRange,
  selectedCustomer,
  topCustomerFilter,
  isActive = false,
  onDateRangeChange,
  onSelectedCustomerChange,
}) => {
  const [getPreferences] = useLazyGetDashboardPreferencesQuery();
  const [saveDashboardPreferences] = useSaveDashboardPreferencesMutation();
  const [selectedMetrics, setSelectedMetrics] = useState([
    'totalQuotations',
    'approvedQuotations',
    'rejectedQuotations',
    'pendingQuotations',
    'avgDaysToApproval',
  ]);
  const [preferencesLoaded, setPreferencesLoaded] = useState(false);
  const isSavingRef = useRef(false);
  const lastSavedPreferencesRef = useRef(null);

  const [
    getMetrics,
    { data: metricsData, isLoading: isLoadingMetrics, error: metricsError },
  ] = useLazyGetQuotationsMetricsQuery();

  const [
    getTrend,
    { data: trendData, isLoading: isLoadingTrend, error: trendError },
  ] = useLazyGetQuotationsTrendQuery();

  const [
    getTopCustomers,
    {
      data: topCustomersData,
      isLoading: isLoadingTopCustomers,
      error: topCustomersError,
    },
  ] = useLazyGetQuotationsTopCustomersQuery();

  const [
    getTopProducts,
    {
      data: topProductData,
      isLoading: isLoadingTopProducts,
      error: topProductsError,
    },
  ] = useLazyGetQuotationsTopProductsQuery();

  const [
    getFormats,
    { data: formatsData, isLoading: isLoadingFormats, error: formatsError },
  ] = useLazyGetQuotationsFormatsQuery();

  const [
    getUserStats,
    {
      data: userStatsData,
      isLoading: isLoadingUserStats,
      error: userStatsError,
    },
  ] = useLazyGetQuotationsCreatedByDataQuery();

  const [
    getConversion,
    {
      data: conversionData,
      isLoading: isLoadingConversion,
      error: conversionError,
    },
  ] = useLazyGetQuotationToSalesOrderConversionQuery();

  const queryParams = useMemo(() => {
    const params = {};

    if (dateRange && dateRange[0] && dateRange[1]) {
      params.dateRange = {
        startDate: dayjs(dateRange[0]).format('YYYY-MM-DD'),
        endDate: dayjs(dateRange[1]).format('YYYY-MM-DD'),
      };
    }

    if (selectedCustomer) {
      params.customerId = selectedCustomer;
    }
    if (topCustomerFilter) {
      params.topCustomerFilter = topCustomerFilter;
    }

    return params;
  }, [dateRange, selectedCustomer, topCustomerFilter]);

  useEffect(() => {
    const loadPreferences = async () => {
      if (!isActive) return;

      try {
        const response = await getPreferences({ tabType: 'quotations' });
        if (response.data) {
          const {
            selectedMetrics: savedMetrics,
            dateRange: savedDateRange,
            selectedCustomer: savedCustomer,
          } = response.data;

          if (savedMetrics && savedMetrics.length > 0) {
            setSelectedMetrics(savedMetrics);
          }

          if (
            savedDateRange &&
            (savedDateRange.startDate || savedDateRange.endDate)
          ) {
            const newDateRange = [
              savedDateRange.startDate ? dayjs(savedDateRange.startDate) : null,
              savedDateRange.endDate ? dayjs(savedDateRange.endDate) : null,
            ];
            if (onDateRangeChange) {
              onDateRangeChange(newDateRange);
            }
          }

          if (savedCustomer && onSelectedCustomerChange) {
            onSelectedCustomerChange(savedCustomer);
          }
        }
      } catch (error) {
        // no need to handle error here
      } finally {
        setPreferencesLoaded(true);
      }
    };

    if (!preferencesLoaded && isActive) {
      loadPreferences();
    }
  }, [
    isActive,
    preferencesLoaded,
    getPreferences,
    onDateRangeChange,
    onSelectedCustomerChange,
  ]);

  // Memoize fetchAllData function to prevent recreation
  const fetchAllData = useCallback(async () => {
    if (!isActive || !preferencesLoaded) return;

    try {
      await Promise.all([
        getMetrics(queryParams),
        getTrend(queryParams),
        getTopCustomers(queryParams),
        getTopProducts({ dateRange: queryParams.dateRange }),
        getFormats(queryParams),
        getConversion(queryParams),
        getUserStats(queryParams),
      ]);
    } catch (error) {
      toast.error('Error fetching quotation dashboard data');
    }
  }, [
    isActive,
    preferencesLoaded,
    queryParams,
    getMetrics,
    getTrend,
    getTopCustomers,
    getTopProducts,
    getFormats,
    getConversion,
    getUserStats,
  ]);
  const hasData = useMemo(() => {
    return !!(
      metricsData?.totalQuotations > 0 ||
      (trendData && trendData.length > 0)
    );
  }, [metricsData, trendData]);

  const transformedStatusData = useMemo(() => {
    if (!metricsData) return [];

    return [
      {
        name: 'Approved',
        value: metricsData.approvedQuotations || 0,
        color: '#059669',
      },
      {
        name: 'Pending',
        value: metricsData.pendingQuotations || 0,
        color: '#d97706',
      },
      {
        name: 'Rejected',
        value: metricsData.rejectedQuotations || 0,
        color: '#dc2626',
      },
    ];
  }, [metricsData]);

  const savePreferences = useCallback(
    async (newPreferences) => {
      if (isSavingRef.current) return;

      const preferencesString = JSON.stringify(newPreferences);
      if (lastSavedPreferencesRef.current === preferencesString) return;

      try {
        isSavingRef.current = true;

        await saveDashboardPreferences({
          tabType: 'quotations',
          preferences: {
            selectedMetrics:
              newPreferences.selectedMetrics !== undefined
                ? newPreferences.selectedMetrics
                : selectedMetrics,
            dateRange:
              newPreferences.dateRange !== undefined
                ? newPreferences.dateRange
                : {
                    startDate:
                      dateRange && dateRange[0] ? dateRange[0].toDate() : null,
                    endDate:
                      dateRange && dateRange[1] ? dateRange[1].toDate() : null,
                  },
            selectedCustomer:
              newPreferences.selectedCustomer !== undefined
                ? newPreferences.selectedCustomer
                : selectedCustomer,
          },
        }).unwrap();

        lastSavedPreferencesRef.current = preferencesString;
      } catch (error) {
        toast.error('Failed to save dashboard preferences');
      } finally {
        isSavingRef.current = false;
      }
    },
    [selectedMetrics, dateRange, selectedCustomer, saveDashboardPreferences]
  );

  const refetch = useMemo(
    () => ({
      metrics: () => getMetrics(queryParams),
      trend: () => getTrend(queryParams),
      topCustomers: () => getTopCustomers({ dateRange: queryParams.dateRange }),
      formats: () => getFormats(queryParams),
      conversion: () => getConversion(queryParams),
      userStats: () => getUserStats(queryParams),
    }),
    [
      queryParams,
      getMetrics,
      getTrend,
      getTopCustomers,
      getFormats,
      getConversion,
      getUserStats,
    ]
  );

  return {
    metrics: metricsData,
    trendData: trendData || [],
    statusData: transformedStatusData,
    conversionData: conversionData || [],
    topCustomers: topCustomersData || [],
    topProducts: topProductData || [],
    formats: formatsData || [],
    userStats: userStatsData || [],

    // Loading States
    isLoadingMetrics,
    isLoadingTrend,
    isLoadingTopCustomers,
    isLoadingTopProducts,
    isLoadingFormats,
    isLoadingConversion,
    isLoadingUserStats,

    // Error States
    metricsError,
    trendError,
    topCustomersError,
    topProductsError,
    formatsError,
    conversionError,
    userStatsError,

    hasData,
    selectedMetrics,
    updateSelectedMetrics: setSelectedMetrics,

    // Preferences
    savePreferences,

    // Actions
    fetchAllData,
    refetch,
  };
};

export default useQuotationDashboard;
