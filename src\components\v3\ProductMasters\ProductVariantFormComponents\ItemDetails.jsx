import { Table } from 'antd';
import { Minus, Palette, Plus, Tag } from 'lucide-react';
import { useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import useDebounceValue from '../../../../hooks/useDebounceValue';
import { useGetStoresQuery } from '../../../../slices/storeApiSlice';
import { useGetAllVendorsForOptionsQuery } from '../../../../slices/vendorApiSlice';
import Input from '../../../global/components/Input';
import MultiSelect from '../../../global/components/MultiSelect';
import Select from '../../../global/components/Select';
import SelectV2 from '../../../global/components/SelectV2';
import AddStoreModal from '../../global/components/AddStoreModal';
import AddVendorModal from '../../global/components/AddVendorModal';
import { InfoTooltip } from './../../../global/components/InfoTooltip';

const ItemDetails = ({
  input,
  inputChangeHandler,
  gstoptions,
  otherThreshold,
  setOtherThreshold,
  changeVendorDetails,
  setInput,
}) => {
  const [StoreSearch, setStoreSearch] = useState('');
  const [VendorSearch, setVendorSearch] = useState('');
  const [modalFor, setModalFor] = useState('');
  const debounceStoreSearch = useDebounceValue(StoreSearch);
  const { data: storeResults } = useGetStoresQuery(debounceStoreSearch || '', {
    refetchOnMountOrArgChange: true,
  });
  const debounceVendorSearch = useDebounceValue(VendorSearch);
  const { data: vendorResults } = useGetAllVendorsForOptionsQuery(
    debounceVendorSearch || '',
    {
      refetchOnMountOrArgChange: true,
    }
  );
  const storesData = storeResults?.stores?.items;

  const handleMultipleThreshold = () => {
    if (otherThreshold?.length >= 2) {
      toast.error('Maximum 2 additional thresholds allowed');
      return;
    }
    setOtherThreshold((prev) => [
      ...prev,
      { value: 0, color: '#3b82f6', tag: '' },
    ]);
  };

  const handleRemoveThreshold = (index) => {
    setOtherThreshold((prev) => prev.filter((_, i) => i !== index));
  };

  const handleOtherThresholdChange = (e, index) => {
    const { name, value } = e.target;
    setOtherThreshold((prev) =>
      prev.map((item, i) => (i === index ? { ...item, [name]: value } : item))
    );
  };

  const canPreferVendor = () => {
    let count = 0;
    for (let i of input?.vendor_details) {
      if (i?.preferred === true) count = count + 1;
    }
    if (count >= 3) return false;
    return true;
  };

  const columns = useMemo(() => [
    {
      title: 'Vendor Name',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => (
        <span className="font-medium cursor-pointer">
          {vendorResults?.find((elem) => elem?.value === record?.vendor)?.label}
        </span>
      ),
    },
    {
      title: 'Rate',
      dataIndex: 'rate',
      key: 'rate',
      render: (_, record) => (
        <Input
          type="number"
          value={record?.rate || ''}
          onChange={(e) => {
            changeVendorDetails(record?.vendor, 'rate', e.target.value);
          }}
          className="w-full h-8 text-sm border-0 bg-white shadow-sm focus:ring-blue-500"
          placeholder="Rate"
        />
      ),
    },
    {
      title: 'Discount (%)',
      dataIndex: 'discount',
      key: 'discount',
      render: (_, record) => (
        <Input
          type="number"
          value={record?.discount || ''}
          onChange={(e) => {
            changeVendorDetails(record?.vendor, 'discount', e.target.value);
          }}
          className="w-full h-8 text-sm border-0 bg-white shadow-sm focus:ring-blue-500"
          placeholder="discount"
        />
      ),
    },
    {
      title: 'Lead Time',
      dataIndex: 'leadTime',
      key: 'leadTime',
      render: (_, record) => (
        <Input
          type="number"
          value={record?.leadTime || ''}
          onChange={(e) => {
            changeVendorDetails(record?.vendor, 'leadTime', e.target.value);
          }}
          className="w-full h-8 text-sm border-0 bg-white shadow-sm focus:ring-blue-500"
          placeholder="leadTime"
        />
      ),
    },
    {
      title: 'Credit Availability',
      dataIndex: 'creditAvailibility',
      key: 'creditAvailibility',
      render: (_, record) => (
        <SelectV2
          options={[
            {
              label: 'Available',
              value: true,
            },
            {
              label: 'Not Available',
              value: false,
            },
          ]}
          value={record?.creditAvailibility || ''}
          onChange={(e) => {
            changeVendorDetails(
              record?.vendor,
              'creditAvailibility',
              e.target.value
            );
          }}
        />
      ),
    },
    {
      title: 'Credit Time',
      dataIndex: 'creditTime',
      key: 'creditTime',
      render: (_, record) => {
        if (record?.creditAvailibility) {
          return (
            <>
              <Input
                type="number"
                value={record?.creditTime || ''}
                onChange={(e) => {
                  changeVendorDetails(
                    record?.vendor,
                    'creditTime',
                    e.target.value
                  );
                }}
                className="w-full h-8 text-sm border-0 bg-white shadow-sm focus:ring-blue-500"
                placeholder="creditTime"
              />
            </>
          );
        } else {
          return <span>-</span>;
        }
      },
    },
    {
      title: 'Preferred',
      dataIndex: 'preferred',
      key: 'preferred',
      render: (_, record) => (
        <span>
          <Input
            type="checkbox"
            checked={record?.preferred || ''}
            onChange={(e) => {
              if (canPreferVendor()) {
                changeVendorDetails(
                  record?.vendor,
                  'preferred',
                  e.target.checked
                );
              } else {
                toast.error('Cannot select more than 3 preferred vendors');
              }
            }}
            className="w-full h-8 text-sm border-0 bg-white shadow-sm focus:ring-blue-500"
          />
        </span>
      ),
    },
  ], [vendorResults, changeVendorDetails, canPreferVendor]);

  const FormField = ({ label, required, children, className = '' }) => (
    <div className={`space-y-1 ${className}`}>
      <label className="flex items-center gap-1 text-sm font-medium text-gray-600">
        {label}
        {required && <span className="text-red-500 text-xs">*</span>}
      </label>
      {children}
    </div>
  );

  const inputClass =
    'h-8 text-sm transition-all duration-200 focus:ring-1 focus:border-transparent';

  return (
    <>
      <div className="mt-4">
        <h2 className="mb-2 mt-6">Item Details</h2>
        <div className="space-y-4">
          {/* Compact grid layout */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-3">
            <FormField label="HSN/SAC Code">
              <Input
                placeholder="HSN/SAC Code"
                name="hsn"
                value={input?.hsn || ''}
                onChange={inputChangeHandler}
                className={`${inputClass} focus:ring-blue-500`}
              />
            </FormField>

            <FormField label="Rate">
              <Input
                placeholder="Rate"
                name="rate"
                value={input?.rate || ''}
                onChange={inputChangeHandler}
                className={`${inputClass} focus:ring-green-500`}
              />
            </FormField>

            <FormField label="Discount (%)">
              <Input
                placeholder="Discount"
                name="discount"
                value={input?.discount || ''}
                onChange={(e) => {
                  if (parseInt(e.target.value) > 100) {
                    toast.error('Discount cannot be greater than 100');
                  } else {
                    inputChangeHandler(e);
                  }
                }}
                className={`${inputClass} focus:ring-green-500`}
              />
            </FormField>
          </div>

          {/* GST in a single row */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
            <FormField label="Error Quantity Per 100">
              <Input
                placeholder="Error Quantity Per 100"
                name="errorPercentage"
                type="number"
                value={input?.errorPercentage || 0}
                onChange={(e) => {
                  setInput((prev) => ({
                    ...prev,
                    errorPercentage: +e.target.value,
                  }));
                }}
                className={`${inputClass} focus:ring-green-500`}
              />
            </FormField>
            {['CGST', 'SGST', 'IGST'].map((gstType) => (
              <FormField key={gstType} label={gstType}>
                <Select
                  className={`w-full ${inputClass} focus:ring-purple-500`}
                  options={gstoptions}
                  placeholder={`Select ${gstType}`}
                  name={gstType.toLowerCase()}
                  value={input?.[gstType.toLowerCase()] || ''}
                  onChange={inputChangeHandler}
                />
              </FormField>
            ))}
          </div>

          {/* Compact Quantity Thresholds */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-600">
                Quantity Thresholds
              </label>
              <InfoTooltip
                id="threshold"
                position="left"
                content="This will be considered as low threshold when you add more than one threshold."
                isHtml="true"
              />
            </div>

            {/* Primary Threshold - Compact */}
            <div className="flex items-center gap-2">
              <Input
                type="number"
                value={input?.quantityThreshold}
                placeholder="Primary threshold"
                name="quantityThreshold"
                onChange={inputChangeHandler}
                className="flex-1 border-0 bg-white text-sm"
              />
              <button
                type="button"
                onClick={handleMultipleThreshold}
                disabled={otherThreshold.length >= 2}
                title="Add additional threshold"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>

            {/* Additional Thresholds - Compact */}
            {otherThreshold.length > 0 && (
              <div className="space-y-2">
                {otherThreshold.map((threshold, index) => (
                  <div key={index} className="flex items-center gap-2 p-2">
                    <Input
                      type="number"
                      value={threshold.value}
                      placeholder="Value"
                      name="value"
                      onChange={(e) => handleOtherThresholdChange(e, index)}
                      className="w-20 border-0 bg-white shadow-sm h-8 text-sm"
                    />
                    <div className="flex-1 relative">
                      <Tag className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
                      <Input
                        type="text"
                        value={threshold.tag}
                        placeholder="Tag"
                        name="tag"
                        onChange={(e) => handleOtherThresholdChange(e, index)}
                        className="border-0 bg-white shadow-sm pl-7 h-8 text-sm"
                      />
                    </div>
                    <div className="relative">
                      <Palette className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400 pointer-events-none" />
                      <input
                        type="color"
                        value={threshold.color}
                        name="color"
                        onChange={(e) => handleOtherThresholdChange(e, index)}
                        className="w-10 h-8 rounded border-0 bg-white shadow-sm cursor-pointer pl-7"
                        title="Color"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveThreshold(index)}
                      className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-all duration-200"
                      title="Remove"
                    >
                      <Minus className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
      {input?.category?.length !== 0 && input?.category !== undefined && (
        <div className="mt-4">
          {modalFor === 'store' && (
            <AddStoreModal setShowModal={() => setModalFor('')} />
          )}
          {modalFor === 'vendor' && (
            <AddVendorModal
              openVendorAddModal={modalFor === 'vendor' ? true : false}
              setOpenVendorAddModal={() => setModalFor('')}
            />
          )}
          <h2 className="mb-2 mt-6">Vendor Details</h2>
          <FormField label="Stores" required>
            <MultiSelect
              value={input?.stores || []}
              placeholder="Select Stores"
              AddOption="+ Add New Store"
              onChange={inputChangeHandler}
              handleAddFunction={() => setModalFor('store')}
              name="stores"
              closeMenuOnSelect={false}
              options={
                storesData?.map((e) => ({ value: e._id, label: e.name })) || []
              }
              onSearch={setStoreSearch}
              doSearch={false}
              className={`${inputClass} focus:ring-blue-500`}
            />
          </FormField>
          {input?.category === 'Outsource Finished Goods' && (
            <FormField label="Vendors" required>
              <MultiSelect
                value={input?.vendors || []}
                placeholder="Select Vendors"
                AddOption="+ Add New Vendor"
                onChange={(e) => {
                  let vendorIds = e.target.value.map((elem) => elem.value);
                  inputChangeHandler({
                    target: { name: 'vendors', value: vendorIds },
                  });
                  let existingVendors = (input?.vendor_details || [])?.map(
                    (elem) => elem?.vendor
                  );
                  let newVendors = vendorIds?.filter(
                    (elem) => !existingVendors?.includes(elem)
                  );
                  let temp = input?.vendor_details;
                  temp = temp?.filter((elem) =>
                    vendorIds?.includes(elem?.vendor)
                  );
                  temp = [
                    ...(temp || []),
                    ...newVendors?.map((elem) => ({
                      vendor: elem,
                      rate: 0,
                      discount: 0,
                      preferred: false,
                    })),
                  ];
                  inputChangeHandler({
                    target: { name: 'vendor_details', value: temp },
                  });
                }}
                handleAddFunction={() => setModalFor('vendor')}
                name="vendors"
                closeMenuOnSelect={false}
                options={
                  vendorResults?.map((e) => ({
                    value: e.value,
                    label: e.name,
                  })) || []
                }
                onSearch={setVendorSearch}
                doSearch={false}
                className={`${inputClass} focus:ring-blue-500`}
              />
            </FormField>
          )}
          {input?.category === 'Outsource Finished Goods' && (
            <Table
              columns={columns}
              dataSource={input?.vendor_details || []}
              rowKey="vendor"
              pagination={false}
              scroll={{ x: 'max-content' }}
              className="shadow-md rounded-lg mt-2"
            />
          )}
        </div>
      )}
    </>
  );
};

export default ItemDetails;
