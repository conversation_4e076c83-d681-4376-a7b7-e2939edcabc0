import { Image as AntdImage, Dropdown, Popover, Select } from 'antd';
import { useContext, useEffect, useMemo, useState } from 'react';
import { IoInformationCircle } from 'react-icons/io5';
import { TiDelete } from 'react-icons/ti';
import { useGetHeaderByPageQuery } from '../../../slices/headerReorderApiSlice';
import { useGetAllTemplatesForOptionsQuery } from '../../../slices/templateApiSlice';
import { Store } from '../../../store/Store';
import { DEFAULT_INHOUSE_WO_HEADER } from '../../../utils/Constant';
import { customConfirm } from '../../../utils/customConfirm';
import HeaderReorder from '../../Pipeline/HeaderReorder';
import Button from '../../global/components/Button';
import OptiInput from '../../global/components/Input';
import Table from '../../global/components/Table';
import VendorSelect from '../../global/components/VendorSelect';
import ProductModal from '../InventoryMasters/ProductModal';
import AssignBomModal from './AssignBomModal';
import RMModal from './RMModal';

const CreateWorkOrderItemsTable = ({
  rows,
  setRows,
  orderQuantity,
  setIndents,
  setStoreRequests,
  allParts,
  allProducts,
}) => {
  const [assignBom, setAssignBom] = useState({
    openModal: false,
    rowIndex: -1,
    record: {},
  });
  const [rmModal, setRmModal] = useState({
    open: false,
    record: {},
    recordIndex: -1,
  });
  const [addProductModal, setAddProductModal] = useState(false);

  const { defaults } = useContext(Store);

  const { data } = useGetAllTemplatesForOptionsQuery();
  const { data: headerInfo } = useGetHeaderByPageQuery({
    headerFor: 'activeWoMachineHeader',
  });

  const [isOpenMachineHeader, setIsOpenMachineHeader] = useState(false);

  const [activeMachineHeader, setActiveMachineHeader] = useState(
    headerInfo?.headers || DEFAULT_INHOUSE_WO_HEADER
  );

  const [allMachineHeader, setAllMachineHeader] = useState([]);

  useEffect(() => {
    if (defaults?.defaultParam?.machineJobColumns) {
      let allCols = defaults?.defaultParam?.machineJobColumns || [];
      let allHead = [];
      allCols?.forEach((item) => {
        const key = item?.field;
        if (!allHead.some((item) => item.key === key)) {
          allHead.push({
            headerName: item?.field?.toUpperCase(),
            key: item?.field,
            isAdditional: true,
            type: item?.fieldType || 'string',
            fieldOptions: item?.fieldOptions || [],
          });
        }
      });
      setAllMachineHeader([...DEFAULT_INHOUSE_WO_HEADER, ...allHead]);
    }
  }, [activeMachineHeader]); // eslint-disable-line

  const renderItemOptions = (entry) => {
    let options = [];

    const inhouseFGParts = allParts?.filter((el) =>
      el?.category?.name?.toLowerCase()?.startsWith('inhouse')
    );

    const inhouseFGProducts = allProducts?.filter((el) =>
      el?.category?.startsWith('Inhouse Finished')
    );

    const inhouseFG = [...(inhouseFGParts || []), ...(inhouseFGProducts || [])];

    const outsourceFGParts = allParts?.filter((el) =>
      el?.category?.name?.toLowerCase()?.startsWith('outsource')
    );

    const outsourceFGProducts = allProducts?.filter((el) =>
      el?.category?.startsWith('Outsource Finished')
    );

    const outsourceFG = [
      ...(outsourceFGParts || []),
      ...(outsourceFGProducts || []),
    ];

    switch (entry?.category) {
      case 'subAssembly':
        options.push({ name: '+ Add Product', value: '+' });
        options.push(
          ...(inhouseFG?.map((el) => ({
            name: el?.name,
            value: el?.value,
            type: el?.type,
            category: el?.category?.name || el?.category,
          })) || [])
        );
        break;

      case 'inhouse':
      case 'Inhouse':
        // options.push({ name: '+ Add Part', value: 'add part' });
        options.push({ name: '+ Add Product', value: '+' });
        options.push(
          ...(inhouseFG?.map((el) => ({
            name: el?.name,
            value: el?.value,
            type: el?.type,
            category: el?.category?.name || el?.category,
            data: el,
          })) || [])
        );
        break;

      case 'Outsource':
      case 'outsource':
        // options.push({ name: '+ Add Part', value: 'add part' });
        options.push({ name: '+ Add Product', value: '+' });
        options.push(
          ...(outsourceFG?.map((el) => ({
            name: el?.name,
            value: el?.value,
            type: el?.type,
            category: el?.category?.name || el?.category,
            data: el,
          })) || [])
        );

        break;

      case 'Inventory':
      case 'inventoryItem':
        // options.push({ name: '+ Add Part', value: 'add part' });
        options.push(
          ...(allParts?.map((el) => ({
            name: el?.name,
            value: el?.value,
            type: el?.type,
            category: el?.category?.name || el?.category,
            data: el,
          })) || [])
        );
        break;

      default:
        // options.push({ name: '+ Manual Entry', value: '+' });
        // options.push({ name: '+ Add Part', value: 'add part' });
        break;
    }
    return options;
  };

  const subtractReserved = (data) => {
    if (!data?.quantity) return 0;
    const diff = data?.quantity - (data?.reserved || 0);
    return (diff <= 0 ? 0 : diff) || 0;
  };

  const getInStock = (record) => {
    let all = [...allParts, ...allProducts];
    let item = all?.find((elem) => elem?.value === record?.itemId);
    return `${subtractReserved(item?.part) || 0}\xA0(${
      item?.quantity?.toFixed(2) || 0
    },\xA0${item?.reserved?.toFixed(2) || 0})`;
  };

  const calculateActionItems = useMemo(() => {
    let temp = [];
    let requestTemp = [];
    let applicableRows = rows?.filter(
      (elem) =>
        elem?.manualEntryStatus === false ||
        elem?.manualEntryStatus === undefined
    );
    if (applicableRows && applicableRows?.length > 0) {
      for (let item of applicableRows) {
        let requiredStock = item?.units * orderQuantity || 0;
        let all = [...allParts, ...allProducts];
        let chosenItem = all?.find((elem) => elem?.value === item?.itemId);
        let inStock = chosenItem?.quantity?.toFixed(2) || 0;
        let diffL1 = inStock - requiredStock;
        if (diffL1 < 0) {
          temp?.push({
            name: chosenItem?.name,
            id: item?.itemId,
            type: chosenItem?.type,
            uom: chosenItem?.uom,
            quantity: Math.abs(diffL1),
            orderQuantity: Math.abs(diffL1),
            vendor: '',
          });
          requestTemp?.push({
            name: chosenItem?.name,
            id: item?.itemId,
            type: chosenItem?.type,
            uom: chosenItem?.uom,
            quantity: inStock,
            orderQuantity: inStock,
            vendor: '',
          });
        }
        if (diffL1 >= 0) {
          requestTemp?.push({
            name: chosenItem?.name,
            id: item?.itemId,
            type: chosenItem?.type,
            uom: chosenItem?.uom,
            quantity: requiredStock,
            orderQuantity: requiredStock,
            vendor: '',
          });
        }
        if (item?.rawMaterials?.length > 0) {
          for (let i of item?.rawMaterials) {
            let requiredStock = item?.units * i?.units * orderQuantity || 0;
            let all = [...allParts, ...allProducts];
            let chosenItem = all?.find(
              (elem) => elem?.value === (i?._id || i?.value)
            );
            let inStock = chosenItem?.quantity?.toFixed(2) || 0;
            let diff = inStock - requiredStock;
            if (diff < 0) {
              temp?.push({
                name: chosenItem?.name,
                id: i?._id || i?.value,
                type: chosenItem?.type,
                uom: chosenItem?.uom || 'N/A',
                quantity: Math.abs(diff),
                orderQuantity: Math.abs(diff),
                vendor: '',
              });
              requestTemp?.push({
                name: chosenItem?.name,
                id: i?.itemId,
                type: chosenItem?.type,
                uom: chosenItem?.uom || 'N/A',
                quantity: inStock,
                orderQuantity: inStock,
                vendor: '',
              });
            }
            if (diff >= 0) {
              requestTemp?.push({
                name: chosenItem?.name,
                id: i?.itemId,
                type: chosenItem?.type,
                uom: chosenItem?.uom || 'N/A',
                quantity: requiredStock,
                orderQuantity: requiredStock,
                vendor: '',
              });
            }
          }
        }
        if (item?.bom?.item?.length > 0) {
          for (let i of item?.bom?.item) {
            let requiredStock =
              item?.units * i?.multiplier * orderQuantity || 0;
            let all = [...allParts, ...allProducts];
            let chosenItem = all?.find(
              (elem) => elem?.value === (i?._id || i?.value)
            );
            let inStock =
              chosenItem?.quantity?.toFixed(2) -
                (chosenItem?.reserved?.toFixed(2) || 0) <
              0
                ? 0
                : chosenItem?.quantity?.toFixed(2) -
                  (chosenItem?.reserved?.toFixed(2) || 0);
            let diffL2 = inStock - requiredStock;
            if (diffL2 < 0) {
              temp?.push({
                name: chosenItem?.name,
                id: i?._id || i?.value,
                type: chosenItem?.type,
                uom: chosenItem?.uom || 'N/A',
                quantity: Math.abs(diffL2),
                orderQuantity: Math.abs(diffL2),
                vendor: '',
              });
              requestTemp?.push({
                name: chosenItem?.name,
                id: i?.itemId,
                type: chosenItem?.type,
                uom: chosenItem?.uom || 'N/A',
                quantity: inStock,
                orderQuantity: inStock,
                vendor: '',
              });
            }
            if (diffL2 >= 0) {
              requestTemp?.push({
                name: chosenItem?.name,
                id: i?.itemId,
                type: chosenItem?.type,
                uom: chosenItem?.uom || 'N/A',
                quantity: requiredStock,
                orderQuantity: requiredStock,
                vendor: '',
              });
            }
            if (i?.rawMaterials?.length > 0) {
              for (let j of i?.rawMaterials) {
                let requiredStock =
                  item?.units * i?.multiplier * orderQuantity * j?.units || 0;
                let all = [...allParts, ...allProducts];
                let chosenItem = all?.find(
                  (elem) => elem?.value === (j?.item?._id || j?.value)
                );
                let inStock = chosenItem?.quantity?.toFixed(2) || 0;
                let diff = inStock - requiredStock;
                if (diff < 0) {
                  temp?.push({
                    name: chosenItem?.name,
                    id: j?._id || j?.value,
                    type: chosenItem?.type,
                    uom: chosenItem?.uom || 'N/A',
                    quantity: Math.abs(diff),
                    orderQuantity: Math.abs(diff),
                    vendor: '',
                  });
                  requestTemp?.push({
                    name: chosenItem?.name,
                    id: j?.itemId,
                    type: chosenItem?.type,
                    uom: chosenItem?.uom || 'N/A',
                    quantity: inStock,
                    orderQuantity: inStock,
                    vendor: '',
                  });
                }
                if (diff >= 0) {
                  requestTemp?.push({
                    name: chosenItem?.name,
                    id: j?.itemId,
                    type: chosenItem?.type,
                    uom: chosenItem?.uom || 'N/A',
                    quantity: requiredStock,
                    orderQuantity: requiredStock,
                    vendor: '',
                  });
                }
              }
            }
          }
        }
      }
    }
    return {
      indents: temp,
      stockRequest: requestTemp,
    };
  }, [rows, orderQuantity]); //eslint-disable-line

  function combineDuplicates(arr) {
    const result = [];
    arr.forEach((item) => {
      // Check if the item with the same ID already exists in the result
      const existingItem = result.find((i) => i.id === item.id);

      if (existingItem) {
        // If it exists, add the quantity to the existing item's quantity
        existingItem.orderQuantity += item.orderQuantity;
        existingItem.quantity += item.quantity;
      } else {
        // If it doesn't exist, push a new item to the result
        result.push({ ...item });
      }
    });

    return result;
  }

  useEffect(() => {
    if (calculateActionItems?.indents) {
      let temp = combineDuplicates(calculateActionItems?.indents);
      setIndents(temp);
    }
    if (calculateActionItems?.stockRequest) {
      let temp = combineDuplicates(calculateActionItems?.indents);
      setStoreRequests(temp);
    }
  }, [calculateActionItems]); //eslint-disable-line

  const changeHandler = (key, name, val) => {
    if (name === 'itemId') {
      let part = allParts?.find((elem) => elem?.value === val);
      let product = allProducts?.find((elem) => elem?.value === val);
      setRows((prev) =>
        prev?.map((elem) => {
          if ((elem?.key || elem?._id) !== key) return elem;
          else {
            return {
              ...elem,
              [name]: val,
              itemType: part ? 'Part' : product ? 'Product' : null,
            };
          }
        })
      );
    } else {
      setRows((prev) =>
        prev?.map((elem) => {
          if ((elem?.key || elem?._id) !== key) return elem;
          else {
            return {
              ...elem,
              [name]: val,
            };
          }
        })
      );
    }
  };

  const emptyRowState = (key) => {
    setRows((prev) =>
      prev?.map((elem) => {
        if (elem?.key !== key) return elem;
        else {
          return {
            ...elem,
            category: '',
            itemName: '',
            itemId: '',
            units: '',
            inStock: 0,
            requiredStock: 0,
            vendor: '',
            attachments: [],
          };
        }
      })
    );
  };

  const setBomModalState = (val, index, record) => {
    setAssignBom({
      openModal: val,
      rowIndex: index !== undefined ? index : -1,
      record,
    });
  };

  const deleteRecord = async (index) => {
    const confirm = await customConfirm(
      'Are you sure you want to remove this item?',
      'delete'
    );
    if (confirm) {
      setRows((elem) => elem?.filter((_, itemIndex) => itemIndex !== index));
    }
  };

  const renderTableRows = () => {
    let cols = activeMachineHeader?.filter(
      (elem) => elem?.key !== 'uom' && elem?.key !== 'attachment'
    );
    return rows?.map((record, index) => {
      return (
        <Table.Row key={index}>
          {cols?.map((el, col_index) => {
            switch (el?.key) {
              case '#':
                return (
                  <Table.Td key={`${index}-${col_index}`}>
                    <span>{index + 1}</span>
                  </Table.Td>
                );
              case 'category':
                return (
                  <Table.Td>
                    <span>
                      <Select
                        virtual={false}
                        placeholder="Select Category"
                        style={{
                          width: '100%',
                          minWidth: '8rem',
                        }}
                        options={[
                          {
                            label: 'Inhouse',
                            value: 'inhouse',
                          },
                          {
                            label: 'Inventory',
                            value: 'Inventory',
                          },
                          {
                            label: 'Outsource',
                            value: 'outsource',
                          },
                        ]}
                        value={record?.category}
                        onChange={(e) => {
                          emptyRowState(record?.key);
                          changeHandler(
                            record?.key || record?._id,
                            'category',
                            e
                          );
                        }}
                      />
                    </span>
                  </Table.Td>
                );
              case 'itemName': {
                const content = (
                  <div className="border-t-[1px] border-slate-200 py-2">
                    <div className="flex items-center gap-2 justify-between">
                      <h5 className="text-slate-600">Quantity: </h5>
                      <p>{record?.soData?.quantity || '-'}</p>
                    </div>
                    <div className="flex items-center gap-2 justify-between">
                      <h5 className="text-slate-600">Rate: </h5>
                      <p>{record?.soData?.rate || '-'}</p>
                    </div>
                    <div className="flex items-center gap-2 justify-between">
                      <h5 className="text-slate-600">Discount: </h5>
                      <p>{record?.soData?.discount || '-'}</p>
                    </div>
                    <div className="flex items-center gap-2 justify-between">
                      <h5 className="text-slate-600">HSN CODE: </h5>
                      <p>{record?.soData?.hsn || '-'}</p>
                    </div>
                    <div className="flex items-center gap-2 justify-between">
                      <h5 className="text-slate-600">SGST: </h5>
                      <p>{record?.soData?.sgst || '-'}</p>
                    </div>
                    <div className="flex items-center gap-2 justify-between">
                      <h5 className="text-slate-600">IGST: </h5>
                      <p>{record?.soData?.igst || '-'}</p>
                    </div>
                    <div className="flex items-center gap-2 justify-between">
                      <h5 className="text-slate-600">CGST: </h5>
                      <p>{record?.soData?.cgst || '-'}</p>
                    </div>
                    <div className="flex items-center gap-2 justify-between">
                      <h5 className="text-slate-600">Amount: </h5>
                      <p>{record?.soData?.amount || '-'}</p>
                    </div>
                    <div className="">
                      <h5 className="text-slate-600">Attachment: </h5>
                      <AntdImage
                        width={200}
                        src={record?.soData?.attachments?.data}
                      />
                    </div>
                  </div>
                );
                return (
                  <Table.Td>
                    <span className="flex items-center gap-2">
                      <Select
                        virtual={false}
                        placeholder="Select Item"
                        className="w-full min-w-[18rem] max-w-[18rem] "
                        options={renderItemOptions(record)?.map((elem) => ({
                          ...elem,
                          label: elem?.name,
                        }))}
                        value={record?.itemId}
                        filterOption={(input, option) =>
                          option?.label
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        showSearch
                        onChange={(e) => {
                          if (e === '+') {
                            setAddProductModal(true);
                          } else {
                            changeHandler(
                              record?.key || record?._id,
                              'itemId',
                              e
                            );
                          }
                        }}
                      />
                      {record?.soData && (
                        <Popover content={content} title="Sales Information">
                          <IoInformationCircle className="text-xl text-blue-500" />
                        </Popover>
                      )}
                    </span>
                  </Table.Td>
                );
              }
              case 'units':
                return (
                  <Table.Td>
                    <span>
                      <OptiInput
                        placeholder="Enter Units"
                        style={{
                          minWidth: '8rem',
                        }}
                        value={record?.units}
                        type="Number"
                        onChange={(e) => {
                          changeHandler(
                            record?.key || record?._id,
                            'units',
                            parseInt(e.target.value)
                          );
                        }}
                      />
                    </span>
                  </Table.Td>
                );
              case 'vendor': {
                let item = record?.itemId;
                const outsourceFGParts = allParts?.filter((el) =>
                  el?.category?.name?.toLowerCase()?.startsWith('outsource')
                );

                const outsourceFGProducts = allProducts?.filter((el) =>
                  el?.category?.startsWith('Outsource Finished')
                );

                const outsourceFG = [
                  ...(outsourceFGParts || []),
                  ...(outsourceFGProducts || []),
                ];
                let foundItem = outsourceFG?.find(
                  (elem) => elem?.value === item
                );
                let preferredVendors = foundItem?.vendor_details
                  ?.filter((elem) => elem?.preferred)
                  ?.map((elem) => elem?.vendor);
                return (
                  <Table.Td>
                    <span>
                      {record?.category === 'outsource' ? (
                        <VendorSelect
                          value={record?.vendor}
                          preferredVendors={preferredVendors}
                          changeHandler={(e) => {
                            let value = e.target.value?.[0]?.value;
                            changeHandler(
                              record?.key || record?._id,
                              'vendor',
                              value
                            );
                          }}
                          inputClass="!w-[12rem]"
                        />
                      ) : (
                        '-'
                      )}
                    </span>
                  </Table.Td>
                );
              }
              case 'inStock':
                return (
                  <Table.Td>
                    <span>{getInStock(record)}</span>
                  </Table.Td>
                );
              case 'requiredStock':
                return (
                  <Table.Td>
                    <span>{record?.units * orderQuantity || 0}</span>
                  </Table.Td>
                );
              case 'action': {
                let items = data
                  ?.filter((elem) => elem?.isForWorkOrder)
                  ?.map((elem) => ({
                    label: elem?.name,
                    key: elem?._id,
                  }));

                const handleMenuClick = (e) => {
                  changeHandler(
                    record?.key || record?._id,
                    'jobTemplate',
                    e?.key
                  );
                };
                const menuProps = {
                  items,
                  onClick: handleMenuClick,
                };
                return (
                  <Table.Td>
                    <div className="flex items-center gap-2 w-full">
                      {(record?.rawMaterials?.length === 0 ||
                        record?.rawMaterials === undefined) &&
                        record?.category === 'inhouse' &&
                        record?.itemId && (
                          <Button
                            size="sm"
                            className="bg-green-500 text-white min-w-[8rem] hover:bg-green-400 w-full"
                            disabled={record?.itemId?.length > 0 ? false : true}
                            onClick={() => {
                              if (record?.itemId?.length > 0) {
                                setBomModalState(true, index, record);
                              }
                            }}
                          >
                            {record?.bom?.item?.length > 0
                              ? 'View Bom'
                              : '+ Add Bom'}
                          </Button>
                        )}
                      {(record?.bom?.item?.length === 0 ||
                        record?.bom === undefined) &&
                        record?.category !== 'Inventory' &&
                        record?.category?.length > 0 &&
                        record?.itemId && (
                          <Button
                            className="bg-orange-500 w-full min-w-[8rem] hover:bg-orange-400"
                            size="sm"
                            disabled={record?.itemId?.length > 0 ? false : true}
                            onClick={() => {
                              setRmModal({
                                open: true,
                                record: record,
                                recordIndex: index,
                              });
                            }}
                          >
                            {record?.rawMaterials?.length > 0
                              ? 'View RM'
                              : '+ Select RM'}
                          </Button>
                        )}
                      {(record?.bom?.item?.length === 0 ||
                        record?.bom === undefined) &&
                        record?.category !== 'outsource' &&
                        record?.category !== 'Inventory' &&
                        record?.category?.length > 0 && (
                          <Dropdown.Button
                            menu={menuProps}
                            arrow={true}
                            disabled={record?.itemId?.length > 0 ? false : true}
                            type="primary"
                            size="medium"
                          >
                            {record?.jobTemplate
                              ? data?.find(
                                  (elem) => elem?._id === record?.jobTemplate
                                )?.name
                              : '+ Add Job Template'}
                          </Dropdown.Button>
                        )}
                      <div>
                        <TiDelete
                          className="text-3xl text-red-500 hover:text-red-400 cursor-pointer"
                          onClick={() => deleteRecord(index)}
                        />
                      </div>
                    </div>
                  </Table.Td>
                );
              }
            }
          })}
        </Table.Row>
      );
    });
  };

  useEffect(() => {
    if (defaults?.defaultParam?.machineJobColumns) {
      let allCols = defaults?.defaultParam?.machineJobColumns || [];
      let allHead = [];
      allCols?.forEach((item) => {
        const key = item?.field;
        if (!allHead.some((item) => item.key === key)) {
          allHead.push({
            headerName: item?.field?.toUpperCase(),
            key: item?.field,
            isAdditional: true,
            type: item?.fieldType || 'string',
            fieldOptions: item?.fieldOptions || [],
          });
        }
      });
      setAllMachineHeader([...DEFAULT_INHOUSE_WO_HEADER, ...allHead]);
    }
  }, [activeMachineHeader]); // eslint-disable-line

  return (
    <>
      {isOpenMachineHeader && (
        <HeaderReorder
          activeHeaderFromLocal={'activeWoMachineHeader'}
          setIsOpenHeaders={setIsOpenMachineHeader}
          activeHeader={activeMachineHeader}
          setActiveHeader={setActiveMachineHeader}
          allHeader={allMachineHeader}
        />
      )}
      <ProductModal
        openModal={addProductModal}
        setOpenModal={setAddProductModal}
      />
      {rmModal?.open && (
        <RMModal
          openModal={rmModal?.open}
          setOpenModal={setRmModal}
          record={rmModal?.record}
          recordIndex={rmModal?.recordIndex}
          setBomItems={setRows}
          allParts={allParts}
          orderQuantity={orderQuantity * rmModal?.record?.units}
        />
      )}
      {assignBom?.openModal && (
        <AssignBomModal
          openModal={assignBom?.openModal}
          setOpenModal={setBomModalState}
          allParts={allParts}
          allProducts={allProducts}
          orderQuantity={orderQuantity * assignBom?.record?.units}
          setRows={setRows}
          rowIndex={assignBom?.rowIndex}
          record={assignBom?.record}
          templateOption={data}
          onClose={() => {
            setAssignBom({
              openModal: false,
              rowIndex: -1,
              record: {},
            });
          }}
        />
      )}

      <Table>
        <Table.Head>
          <Table.Row>
            {activeMachineHeader
              ?.filter(
                (elem) => elem?.key !== 'uom' && elem?.key !== 'attachment'
              )
              ?.map((el, index) => {
                return <Table.Th key={index}>{el?.headerName}</Table.Th>;
              })}

            <Table.Options
              className={'!p-2 border-b-2 bg-[#f8f6fc] '}
              onRearrange={() => {
                setIsOpenMachineHeader(true);
              }}
            />
          </Table.Row>
        </Table.Head>
        <Table.Body>{renderTableRows()}</Table.Body>
      </Table>
    </>
  );
};

export default CreateWorkOrderItemsTable;
