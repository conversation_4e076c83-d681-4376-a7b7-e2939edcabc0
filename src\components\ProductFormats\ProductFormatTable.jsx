import {
  BgColorsOutlined,
  DeleteOutlined,
  DragOutlined,
  EditOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { EllipsisVerticalIcon } from '@heroicons/react/24/outline';
import { Button, Dropdown, Popover, Tooltip } from 'antd';
import { all, create } from 'mathjs';
import { memo, useCallback, useMemo, useRef, useState } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { TbDragDrop } from 'react-icons/tb';
import { v4 as uuidv4 } from 'uuid';
import { camelCaseString } from '../../helperFunction';
import { useGetDropdownsQuery } from '../../slices/dropdownApiSlice';
import CircleColorPicker from '../global/components/CircleColorPicker';
import Input from '../global/components/Input';
import SelectV2 from '../global/components/SelectV2';
import AddProductInProductFormatTable from './AddProductInProductFormatTable';
import { LIGHT_ROW_COLORS } from './LightColor';

const math = create(all, {
  number: 'number',
  precision: 64,
});

const ItemTypes = {
  ROW: 'row',
};

// Memoized TableCell component
const TableCell = memo(({ children, className = '' }) => {
  return (
    <td className={`border px-4 py-2 text-sm ${className}`}>
      <div>{children}</div>
    </td>
  );
});
TableCell.displayName = 'TableCell';

// Memoized Input Cell component
const InputCell = memo(({ value, onChange, type, placeholder, formatting }) => {
  let symbol = '';
  if (formatting?.isItPrice) {
    symbol = '₹';
  }
  return (
    <div className="flex items-center gap-[2px]">
      {symbol && <span className="text-xs">{symbol}</span>}
      <Tooltip title={value}>
        <Input
          value={value}
          onChange={onChange}
          type={type}
          placeholder={placeholder}
          className="w-full text-sm rounded-md bg-white"
          min={type === 'number' ? 0 : undefined}
        />
      </Tooltip>
      {formatting?.isItPercentage && (
        <span className="text-xs text-gray-500">%</span>
      )}
    </div>
  );
});
InputCell.displayName = 'InputCell';

// Memoized Select Cell component
const SelectCell = memo(
  ({ value, onChange, options, placeholder, formatting }) => {
    let symbol = '';
    if (formatting?.isItPrice) {
      symbol = '₹';
    }
    return (
      <div className="flex items-center gap-[2px]">
        {symbol && <span className="text-xs">{symbol}</span>}
        <SelectV2
          menuPosition="fixed"
          options={options}
          onChange={onChange}
          value={value || ''}
          placeholder={placeholder}
          variant="outline"
          size="small"
          className="bg-white rounded-md"
        />
      </div>
    );
  }
);
SelectCell.displayName = 'SelectCell';

// Memoized Formula Cell component
const FormulaCell = memo(({ value, formatting }) => {
  let symbol = '';
  if (formatting?.isItPrice) {
    symbol = '₹';
  }
  return (
    <div className="text-right font-medium flex items-center gap-[2px]">
      {symbol && <span className="text-xs">{symbol}</span>}
      {!isNaN(value)
        ? formatting?.roundValue
          ? Math.round(value)
          : parseFloat(value).toFixed(2)
        : value || '0.00'}
    </div>
  );
});
FormulaCell.displayName = 'FormulaCell';

const DraggableTableRow = memo(
  ({
    record,
    fields,
    onFieldChange,
    onDelete,
    dropdownOptions,
    isEditing,
    startEditing,
    stopEditing,
    handleEditModeFieldChange,
    index,
    moveRow,
    onRowColorChange,
  }) => {
    const ref = useRef(null);
    const dragHandleRef = useRef(null);

    const [{ isDragging }, drag, preview] = useDrag({
      type: ItemTypes.ROW,
      item: { index },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    const [, drop] = useDrop({
      accept: ItemTypes.ROW,
      hover(item, monitor) {
        if (!ref.current) return;
        const dragIndex = item.index;
        const hoverIndex = index;
        if (dragIndex === hoverIndex) return;
        const hoverBoundingRect = ref.current.getBoundingClientRect();
        const hoverMiddleY =
          (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
        const clientOffset = monitor.getClientOffset();
        const hoverClientY = clientOffset.y - hoverBoundingRect.top;
        if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
        if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;
        moveRow(dragIndex, hoverIndex);
        item.index = hoverIndex;
      },
    });
    preview(drop(ref));
    drag(dragHandleRef);

    const taxRateOptions = dropdownOptions
      ?.find((e) => e.name === 'taxRates')
      ?.values?.map((option) => ({
        label: `${option}%`,
        value: `${option}%`, // Keep the % symbol in the value for display consistency
      })) || [
      { label: '5%', value: '5%' },
      { label: '12%', value: '12%' },
      { label: '18%', value: '18%' },
      { label: '28%', value: '28%' },
    ];

    return (
      <tr
        ref={ref}
        className={`bg-white transition-all duration-150`}
        style={{
          cursor: isDragging ? 'grabbing' : 'default',
          backgroundColor: record.rowColor || undefined,
        }}
      >
        <TableCell className="w-10 text-center">
          <span
            ref={dragHandleRef}
            style={{ cursor: 'grab', display: 'inline-block' }}
            title="Drag to reorder"
            tabIndex={0}
          >
            <DragOutlined className="text-gray-400 cursor-grab active:cursor-grabbing" />
          </span>
        </TableCell>
        <TableCell className="w-10 text-center">
          <span>{index + 1}</span>
        </TableCell>
        {fields?.map((field) => {
          const camelFieldName = camelCaseString(field.fieldName);
          const value = field.isDefault
            ? record[camelFieldName]
            : record.customColumns?.[camelFieldName];

          return (
            <TableCell
              key={`${record.key}-${camelFieldName}`}
              className="min-w-[12rem]"
            >
              {field.fieldType === 'formula' && !isEditing ? (
                <FormulaCell value={value} formatting={field.formatting} />
              ) : field.fieldType === 'dropdown' ||
                field.fieldType === 'taxDropdown' ? (
                <SelectCell
                  value={value}
                  onChange={(e) =>
                    isEditing
                      ? handleEditModeFieldChange(
                          record.key,
                          camelFieldName,
                          e.target.value
                        )
                      : onFieldChange(
                          record.key,
                          camelFieldName,
                          e.target.value
                        )
                  }
                  options={
                    /cgst|sgst|igst/.test(camelFieldName) &&
                    camelFieldName.includes('rate')
                      ? taxRateOptions
                      : field.dropdownOptions?.map((option) => ({
                          label: option,
                          value: option,
                        }))
                  }
                  placeholder="Select"
                  formatting={field.formatting}
                />
              ) : camelFieldName.toLowerCase() === 'uom' && field?.isDefault ? (
                <SelectCell
                  value={value}
                  onChange={(e) =>
                    onFieldChange(record.key, camelFieldName, e.target.value)
                  }
                  options={dropdownOptions
                    ?.find((e) => e.name === 'uom')
                    ?.values?.map((option) => ({
                      label: option,
                      value: option,
                    }))}
                  placeholder="Select UOM"
                  formatting={field.formatting}
                />
              ) : (
                <InputCell
                  value={value}
                  onChange={(e) =>
                    isEditing
                      ? handleEditModeFieldChange(
                          record.key,
                          camelFieldName,
                          e.target.value
                        )
                      : onFieldChange(
                          record.key,
                          camelFieldName,
                          e.target.value
                        )
                  }
                  type={field.fieldType === 'number' ? 'number' : 'text'}
                  placeholder={`Enter ${field.fieldName}`}
                  formatting={field.formatting}
                />
              )}
            </TableCell>
          );
        })}
        <TableCell className="text-center">
          {isEditing ? (
            <div className="flex gap-2 justify-center">
              <Button
                onClick={() => stopEditing(record.key)}
                className="bg-green-500 hover:bg-green-600 text-white"
              >
                Save
              </Button>
              <Button
                onClick={() => {
                  stopEditing(record.key);
                }}
                className="bg-gray-200 hover:bg-gray-300"
              >
                Cancel
              </Button>
            </div>
          ) : (
            <div className="flex gap-2 justify-center items-center">
              <Button
                type="link"
                onClick={() => startEditing(record.key)}
                className="text-blue-500 hover:text-blue-700"
                icon={<EditOutlined />}
              />
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => onDelete(record.key)}
                className="text-red-500 hover:text-red-700"
              />
              <Popover
                placement="bottom"
                content={
                  <CircleColorPicker
                    value={record.rowColor || '#fff'}
                    onChange={(color) => onRowColorChange(record.key, color)}
                    circleSize={20}
                    colors={LIGHT_ROW_COLORS.map((color) => color.value)}
                  />
                }
              >
                <Button
                  type="link"
                  icon={<BgColorsOutlined />}
                  className="text-blue-500 hover:text-blue-700"
                />
              </Popover>
            </div>
          )}
        </TableCell>
      </tr>
    );
  }
);
DraggableTableRow.displayName = 'DraggableTableRow';

const ProductFormatTable = ({
  format = {},
  onEditFormat,
  onCopyFormat,
  onDeleteFormat,
  input,
  setInput,
  columnVisibility,
  setColumnVisibility,
  visibleFields = [],
}) => {
  const [openModal, setOpenModal] = useState(false);
  const [editingRows, setEditingRows] = useState(new Set());
  const { data: dropdownsData } = useGetDropdownsQuery();
  const visibleFieldIds =
    visibleFields && visibleFields.map((field) => field.fieldId);
  const filteredFields = visibleFieldIds
    ? format.fields.filter((field) => visibleFieldIds.includes(field.fieldId))
    : format?.fields;
  const conditions = useMemo(
    () => format.conditions || [],
    [format.conditions]
  );

  const hasCGST = format?.fields?.some((field) =>
    camelCaseString(field?.fieldName)?.includes('cgst')
  );
  const hasSGST = format?.fields?.some((field) =>
    camelCaseString(field?.fieldName)?.includes('sgst')
  );

  const toggleColumnVisibility = (fieldName) => {
    setColumnVisibility((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }));
  };

  // Helper function to evaluate conditions
  const evaluateCondition = useCallback(
    (record, fieldName, calculatedValue) => {
      const condition = conditions.find((c) => c.targetField === fieldName);
      if (!condition) return calculatedValue;

      // Check each condition rule
      for (const rule of condition.conditions) {
        if (!rule.field || !rule.operator || rule.value === '') continue;

        const compareValue = math.isNaN(rule.value)
          ? 0
          : math.number(rule.value);

        // Evaluate the condition
        let conditionMet = false;
        switch (rule.operator) {
          case '>':
            conditionMet = calculatedValue > compareValue;
            break;
          case '<':
            conditionMet = calculatedValue < compareValue;
            break;
          case '=':
            conditionMet = calculatedValue === compareValue;
            break;
          case '>=':
            conditionMet = calculatedValue >= compareValue;
            break;
          case '<=':
            conditionMet = calculatedValue <= compareValue;
            break;
          case '!=':
            conditionMet = calculatedValue !== compareValue;
            break;
          default:
            conditionMet = false;
        }
        // If condition is met, return the result from condition
        if (conditionMet) {
          return rule.result !== ''
            ? math.number(rule.result)
            : calculatedValue;
        }
      }

      // If no conditions match, return the calculated value
      return calculatedValue;
    },
    [conditions]
  );

  const calculateFormula = useCallback(
    (record, formula, shouldRoundValue, fieldName) => {
      try {
        const fieldPattern = /\b[A-Za-z][A-Za-z0-9_ ]*\b/g;
        let calculationFormula = formula;

        // Handle SUM, AVG, MIN, MAX functions
        const functionMatch = formula.match(/^(SUM|AVG|MIN|MAX)/);
        if (functionMatch) {
          const functionName = functionMatch[1];
          const fields = formula.match(fieldPattern) || [];
          fields.shift(); // Remove the function name from the array

          const values = fields.map((field) => {
            const camelField = camelCaseString(field);
            let value =
              record[camelField] ?? record.customColumns?.[camelField];

            // Convert percentage values to decimal
            if (typeof value === 'string' && value.includes('%')) {
              value = math.divide(parseFloat(value.replace('%', '')), 100);
            }

            return math.isNaN(value) ? 0 : math.number(value);
          });

          if (values.length === 0) return 0;

          let result;
          switch (functionName) {
            case 'SUM':
              result = math.sum(values);
              break;
            case 'AVG':
              result = math.mean(values);
              break;
            case 'MIN':
              result = math.min(values);
              break;
            case 'MAX':
              result = math.max(values);
              break;
            default:
              result = 0;
          }

          // Apply conditions to the result if field name is provided
          if (fieldName) {
            result = evaluateCondition(record, fieldName, result);
          }

          return shouldRoundValue ? Math.round(result) : math.round(result, 2);
        }

        // For other formulas, continue with existing logic
        const fields = formula.match(fieldPattern) || [];

        fields.forEach((field) => {
          const camelField = camelCaseString(field);
          let value = record[camelField] ?? record.customColumns?.[camelField];

          if (typeof value === 'string' && value.includes('%')) {
            value = math.divide(parseFloat(value.replace('%', '')), 100);
          }

          value = math.isNaN(value) ? 0 : math.number(value);

          calculationFormula = calculationFormula.replace(
            new RegExp(`\\b${field.replace(/\s+/g, '\\s+')}\\b`, 'g'),
            value.toString()
          );
        });

        let result = math.evaluate(calculationFormula);

        // Apply conditions to the result if field name is provided
        if (fieldName) {
          result = evaluateCondition(record, fieldName, result);
        }

        return shouldRoundValue ? Math.round(result) : math.round(result, 2);
      } catch (error) {
        return 0;
      }
    },
    [evaluateCondition]
  );

  const processData = useCallback(
    (record) => {
      const newRecord = { ...record };
      const pendingCalculations = [];

      format.fields.forEach((field) => {
        const camelFieldName = camelCaseString(field.fieldName);

        if (field.fieldType === 'formula' && field.formula) {
          // If the formula depends on other calculated fields, delay its calculation
          if (field.formula.match(/\b[A-Z][A-Z0-9_/ ]*\b/)) {
            pendingCalculations.push({
              field,
              camelFieldName,
              shouldRoundValue: field.formatting?.roundValue,
            });
          } else {
            const result = calculateFormula(
              newRecord,
              field.formula,
              field.formatting?.roundValue,
              field.fieldName
            );
            if (field.isDefault) {
              newRecord[camelFieldName] = result;
            } else {
              newRecord.customColumns = {
                ...newRecord.customColumns,
                [camelFieldName]: result,
              };
            }
          }
        }
      });

      // Process delayed calculations after other fields are set
      pendingCalculations.forEach(
        ({ field, camelFieldName, shouldRoundValue }) => {
          const result = calculateFormula(
            newRecord,
            field.formula,
            shouldRoundValue,
            field.fieldName
          );
          if (field.isDefault) {
            newRecord[camelFieldName] = result;
          } else {
            newRecord.customColumns = {
              ...newRecord.customColumns,
              [camelFieldName]: result,
            };
          }
        }
      );

      return newRecord;
    },
    [format.fields, calculateFormula]
  );

  const handleFieldChange = useCallback(
    (key, fieldName, value) => {
      setInput((prevData) => {
        return prevData.map((item) => {
          if (item.key !== key) return item;
          const newItem = { ...item };
          const field = format.fields.find((f) => {
            return camelCaseString(f.fieldName) === fieldName;
          });

          const processedValue =
            value === ''
              ? ''
              : typeof value === 'string' && value.includes('%')
                ? value
                : !isNaN(value)
                  ? math.number(value)
                  : value;

          if (field?.isDefault) {
            newItem[fieldName] = processedValue;
          } else {
            newItem.customColumns = {
              ...newItem.customColumns,
              [fieldName]: processedValue,
            };
          }

          return processData(newItem);
        });
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [format.fields, processData]
  );

  const handleEditModeFieldChange = useCallback(
    (key, fieldName, value) => {
      setInput((prevData) => {
        return prevData.map((item) => {
          if (item.key !== key) return item;
          const newItem = { ...item };
          const field = format.fields.find(
            (f) => camelCaseString(f.fieldName) === fieldName
          );

          // During edit mode, we store raw values without calculations
          if (field?.isDefault) {
            newItem[fieldName] = value;
          } else {
            newItem.customColumns = {
              ...newItem.customColumns,
              [fieldName]: value,
            };
          }
          return newItem;
        });
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [format.fields]
  );

  // Function to move a row from one position to another
  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      setInput((prevRows) => {
        const newRows = [...prevRows];
        const draggedRow = newRows[dragIndex];

        newRows.splice(dragIndex, 1);
        newRows.splice(hoverIndex, 0, draggedRow);

        return newRows;
      });
    },
    [setInput]
  );

  const startEditing = (key) => {
    setEditingRows((prev) => new Set(prev).add(key));
  };

  const stopEditing = (key) => {
    setEditingRows((prev) => {
      const newSet = new Set(prev);
      newSet.delete(key);
      return newSet;
    });
  };

  const addRow = () => {
    const newKey = uuidv4();
    const emptyRow = {
      key: newKey,
      customColumns: {},
      rowColor: '',
    };

    format.fields.forEach((field) => {
      const camelFieldName = camelCaseString(field.fieldName);
      if (field.isDefault) {
        emptyRow[camelFieldName] = '';
      } else if (field.fieldType !== 'formula') {
        emptyRow.customColumns[camelFieldName] = '';
      }
    });

    setInput((prevData) => {
      const newData = [...prevData, emptyRow];
      return newData;
    });
  };
  const addFinishGood = (products) => {
    const newData = products.map((product) => {
      const camelCasedProduct = {
        key: uuidv4(),
        customColumns: {},
        rowColor: '',
      };
      // set rowColor
      if (product.color) {
        camelCasedProduct.rowColor = product.color;
      }

      format.fields.forEach((field) => {
        const camelFieldName = camelCaseString(field.fieldName);
        if (camelFieldName === 'productName') {
          camelCasedProduct[camelFieldName] = product.productName;
        } else if (camelFieldName === 'rate') {
          camelCasedProduct[camelFieldName] = product.rate;
        } else if (camelFieldName === 'discount') {
          camelCasedProduct[camelFieldName] = product.discount;
        } else if (camelFieldName === 'cgstRate') {
          camelCasedProduct[camelFieldName] = `${product.cgstRate}%`;
        } else if (camelFieldName === 'sgstRate') {
          camelCasedProduct[camelFieldName] = `${product.sgstRate}%`;
        } else if (camelFieldName === 'igstRate') {
          camelCasedProduct[camelFieldName] = `${product.igstRate}%`;
        } else if (camelFieldName === 'hsnSacCode') {
          camelCasedProduct[camelFieldName] = product.HsnSacCode;
        } else if (camelFieldName === 'uom') {
          camelCasedProduct[camelFieldName] = product.uom;
        } else if (field.isDefault) {
          camelCasedProduct[camelFieldName] = '';
        } else if (field.fieldType !== 'formula') {
          camelCasedProduct.customColumns[camelFieldName] = '';
        }
      });

      return camelCasedProduct;
    });

    setInput((prevData) => [...prevData, ...newData]);
    setOpenModal(false);
  };

  const handleDelete = useCallback(
    (key) => {
      setInput((prevData) => {
        const filteredData = prevData.filter((item) => item.key !== key);
        return filteredData;
      });
    },
    [setInput]
  );

  const handleRowColorChange = useCallback(
    (key, color) => {
      setInput((prevData) =>
        prevData.map((item) =>
          item.key === key ? { ...item, rowColor: color } : item
        )
      );
    },
    [setInput]
  );

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="product-format-table bg-white rounded-lg shadow">
        <div className="flex justify-between items-center p-4 border-b">
          <h4 className="text-xl font-semibold text-gray-800">
            {format.formatName}
          </h4>
          <div className="flex gap-2">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={addRow}
              className="bg-blue-500 hover:bg-blue-600"
            >
              Add Item
            </Button>
            <Button onClick={() => setOpenModal(true)}>Add Finish Good</Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-50">
                <th className="border-b px-4 py-3 text-left text-sm font-medium text-gray-700 w-10">
                  <TbDragDrop
                    className="text-gray-400"
                    title="Drag to reorder"
                  />
                </th>
                <th className="border-b px-4 py-3 text-left text-sm font-medium text-gray-700 w-10">
                  SR NO.
                </th>
                {filteredFields?.map((field) => {
                  return (
                    <th
                      key={field.fieldName}
                      className="border-b px-4 py-3 text-left text-sm font-medium text-gray-700 whitespace-nowrap"
                    >
                      <div className="flex items-center gap-2">
                        <span>{field.fieldName}</span>
                        <Button
                          type="link"
                          icon={
                            columnVisibility[
                              camelCaseString(field.fieldName)
                            ] ? (
                              <EyeInvisibleOutlined />
                            ) : (
                              <EyeOutlined />
                            )
                          }
                          onClick={() =>
                            toggleColumnVisibility(
                              camelCaseString(field.fieldName)
                            )
                          }
                          className="p-0 h-auto"
                        />
                      </div>
                    </th>
                  );
                })}
                <th className="border-b px-4 py-3 text-left text-sm font-medium text-gray-700">
                  Action
                </th>
                <th
                  className={`border-b px-4 py-3 text-left text-sm font-medium text-gray-700 ${visibleFields?.length > 0 ? 'hidden' : ''}`}
                >
                  <Dropdown
                    trigger={['click']}
                    placement="bottomRight"
                    menu={{
                      items: [
                        {
                          key: 'edit',
                          label: 'Edit Format',
                          onClick: () => onEditFormat(format),
                        },
                        {
                          key: 'delete',
                          label: 'Delete Format',
                          onClick: () => onDeleteFormat(format._id),
                        },
                        {
                          key: 'copy',
                          label: 'Copy Format',
                          onClick: () => onCopyFormat(format),
                        },
                      ],
                    }}
                  >
                    <EllipsisVerticalIcon className="h-4 outline-none cursor-pointer" />
                  </Dropdown>
                </th>
              </tr>
            </thead>
            <tbody>
              {input.map((record, index) => (
                <DraggableTableRow
                  key={record.key}
                  record={record}
                  fields={filteredFields}
                  onFieldChange={handleFieldChange}
                  onDelete={handleDelete}
                  dropdownOptions={dropdownsData?.dropdowns}
                  columnVisibility={columnVisibility}
                  isEditing={editingRows.has(record.key)}
                  startEditing={startEditing}
                  stopEditing={stopEditing}
                  handleEditModeFieldChange={handleEditModeFieldChange}
                  index={index}
                  moveRow={moveRow}
                  onRowColorChange={handleRowColorChange}
                />
              ))}
            </tbody>
          </table>
        </div>

        {(hasCGST || hasSGST) && (
          <div className="p-4 border-t text-sm text-gray-600">
            <p>
              Tax Details: Discount, CGST, SGST, IGST are always calculated as
              percentages
            </p>
          </div>
        )}

        <AddProductInProductFormatTable
          openModal={openModal}
          setOpenModal={setOpenModal}
          onAddProduct={addFinishGood}
          format={format}
        />
      </div>
    </DndProvider>
  );
};

export default ProductFormatTable;
