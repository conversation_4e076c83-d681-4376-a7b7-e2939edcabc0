import {
  ArrowLeftOutlined,
} from '@ant-design/icons';
import { Button } from 'antd';
import { useEffect, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { useNavigate, useParams } from 'react-router-dom';
import ManageProductsForm from '../components/ManageProducts/ManageProductsForm';
import { mobileWidth, tabletWidth } from '../helperFunction';
import { useLazyGetProductByIdForEditQuery } from '../slices/productApiSlice';

function ManageProducts() {
  const { id } = useParams();
  const navigate = useNavigate();

  const [selectedTabs, setSelectedTabs] = useState([]);

  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });

  const [getProductById, { data: productData, isLoading: loadingProductData }] =
    useLazyGetProductByIdForEditQuery();

  useEffect(() => {
    if (id) getProductById({ id }, false);
  }, [id, getProductById]);

  const refetchProduct = () => {
    if (id) getProductById({ id }, false);
  };

  return (
    <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
      {/* Header */}
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => {
              navigate('/settings/inventory/products');
            }}
            type="text"
            size="small"
            className="hover:bg-gray-200"
          />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-0">
              {id ? 'Edit' : 'Create'} FG Product
            </h2>
            <p className="text-sm text-gray-600 mb-0">
              {id
                ? 'Update invoice information'
                : 'Create a new invoice entry'}
            </p>
          </div>
        </div>
      </div>
      <ManageProductsForm
        isMobile={isMobile}
        isTablet={isTablet}
        hierarchyIdx={0}
        parent={productData}
        selectedTabs={selectedTabs}
        setSelectedTabs={setSelectedTabs}
        refetchParent={refetchProduct}
        loadingProductData={loadingProductData}
      />
    </div>
  );
}

export default ManageProducts;
