import { useState } from 'react';
import Header from '../components/global/components/Header';
import ProductsTable from '../components/v3/ProductMasters/ProductsTable';

const InventoryProduct = () => {
  const [popup, setPopup] = useState('none');

  return (
    <div className="h-full">
      <div className="flex flex-col">
        <div className="flex gap-[5px] items-center">
          <Header
            title="FG Products"
            description="Manage your products"
            infoTitle="Welcome to the Product Master"
            infoDesc="Your Bill of Materials (BOM) control center."
            paras={[
              'Here, you can effortlessly create new BOMs for your products by assembling parts and sub-assemblies, simplifying your product management with precision and ease.',
              'Simplify product creation with our Product Master – your Bill of Materials (BOM) hub for assembling parts and sub-assemblies.',
            ]}
          />
        </div>
        <ProductsTable popup={popup} setPopup={setPopup} />
      </div>
    </div>
  );
};

export default InventoryProduct;
